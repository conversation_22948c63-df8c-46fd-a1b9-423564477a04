<!doctype html><html lang="en"><head><meta charset="UTF-8"><link rel="icon" href="/favicon.ico"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,maximum-scale=1,minimum-scale=1,viewport-fit=cover"><title>au</title><script type="module" crossorigin src="/h5/feature/js/index-M11nEPjl.js"></script><link rel="modulepreload" crossorigin href="/h5/feature/js/vendor-CwRwASPO.js"><link rel="modulepreload" crossorigin href="/h5/feature/js/vue-vendor-DjIN0JG5.js"><link rel="modulepreload" crossorigin href="/h5/feature/js/vant-vendor-D8PsFlrJ.js"><link rel="stylesheet" crossorigin href="/h5/feature/css/vant-vendor-DMYb2s8A.css"><link rel="stylesheet" crossorigin href="/h5/feature/css/index-ClHlbbJ0.css"></head><body><div id="app"></div></body><script>const darkBgColor = '#1A1D20';
        // 直接进入如果是深色，则设置深色主题背景色
        const searchParams = new URLSearchParams(window.location.search);
        const theme = searchParams.get('theme');
        if (theme === '1') {
            const html = document.querySelector('html');
            html.style.backgroundColor = darkBgColor;
            const body = document.querySelector('body');
            body.style.backgroundColor = darkBgColor;
        }

        // 阻止iOS WebView中的双击缩放行为
        document.addEventListener(
            'touchstart',
            (event) => {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            },
            { passive: false }
        );

        let lastTouchEnd = 0;
        document.addEventListener(
            'touchend',
            (event) => {
                const now = new Date().getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            },
            false
        );</script></html>