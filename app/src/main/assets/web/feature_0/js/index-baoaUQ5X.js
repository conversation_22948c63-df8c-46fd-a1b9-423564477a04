import{p as e,r as t,_ as i}from"./index-M11nEPjl.js";import{s,D as a}from"./vant-vendor-D8PsFlrJ.js";import{D as n,s as o,K as d,J as l,S as r,V as h,B as c}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const p="AU".replace(/AU/g,"vau").toLowerCase(),g=t=>e(`/${p}/setVerifyMethodLegacy`,t,{apiVer:"v1","Content-Type":"multipart/form-data"});var m="js4-Mu6-V2C-k27";const v={class:"container padding_12","loading-img":"au"},u={class:"iv"},A={class:"iv-text"},f={class:"iv-img-box"},y={class:"vyi"},C={class:"upload-id"};const k=i({mixins:[{data:()=>({loading:!1}),methods:{handleClose(){t("501")},hanldeUploadID(){const{token:e}=this.$pinia.state.value.params;e?this.setVerifyMethodLegacyNoPopUp():(t("10"),t("501"))},handleGetData(){this.loading=!0;const{token:i}=this.$pinia.state.value.params;if(!i)return t("501"),void(this.loading=!1);var a;i&&(a={token:this.$pinia.state.value.params.token,step:"5-2"},e(`/${p}/getData`,a,{apiVer:"v2","Content-Type":"multipart/form-data"})).then((e=>{"V00000"===e.resultCode?window.greenidUI.show("vantageglobal",m,e.data.obj.verificationToken):s(e.msgInfo),this.loading=!1})).catch((()=>{this.loading=!1}))},onError(e,t,i,s){},onSessionComplete(e,t){"VERIFIED"===t?this.greenId():"IN_PROGRESS"===t||this.setVerifyMethodLegacy()},registerCallback(e){},onSourceAttempt(e,t){},onSessionCancel(e){},greenId(){var i;this.loading=!0,(i={token:this.$pinia.state.value.params.token},e(`/${p}/greenIdPass`,i,{apiVer:"v1","Content-Type":"multipart/form-data"})).then((e=>{"V00000"===e.resultCode?t("103"):s(e.msgInfo),this.loading=!1})).catch((()=>{this.loading=!1}))},setVerifyMethodLegacy(){this.loading=!0,g({token:this.$pinia.state.value.params.token}).then((e=>{"V00000"===e.resultCode?a.alert({message:this.$t("Your information verification failed, please upload proof of identity for verification."),overlay:!1,confirmButtonText:this.$t("OK")}).then((()=>{t("104")})):s(e.msgInfo),this.loading=!1})).catch((()=>{this.loading=!1}))},setVerifyMethodLegacyNoPopUp(){this.loading=!0,g({token:this.$pinia.state.value.params.token}).then((e=>{"V00000"===e.resultCode?t("104"):s(e.msgInfo),this.loading=!1})).catch((()=>{this.loading=!1}))},loadScript(e){const t=document.createElement("script");t.src=e,t.async=!1,document.head.appendChild(t),t.onload=()=>{window.greenidUI&&this.initGreenId()}},loadStylesheet(e){const t=document.createElement("link");t.rel="stylesheet",t.type="text/css",t.href=e,document.head.appendChild(t)},initGreenId(){window.greenidUI.setup({environment:"prod",frameId:"greenid-div",errorCallback:this.onError,sessionCompleteCallback:this.onSessionComplete,sourceAttemptCallback:this.onSourceAttempt,sessionCancelledCallback:this.onSessionCancel,registerCallback:this.registerCallback}),window.greenidConfig.setOverrides()}},created(){this.loadScript("https://simpleui-au.vixverify.com/df/javascripts/greenidConfig.js"),this.loadScript("https://simpleui-au.vixverify.com/df/javascripts/greenidui.min.js"),this.loadStylesheet("https://simpleui-au.vixverify.com/df/assets/stylesheets/greenid.css")},mounted(){this.handleGetData()}}]},[["render",function(e,t,i,s,a,p){const g=n("loading");return o((d(),l("div",v,[t[2]||(t[2]=r("link",{rel:"stylesheet",type:"text/css",media:"screen",href:"https://simpleui-au.vixverify.com/df/assets/stylesheets/greenid.css"},null,-1)),r("div",u,[r("div",A,h(e.$t("Identity Verification")),1),r("div",f,[r("img",{onClick:t[0]||(t[0]=(...t)=>e.handleClose&&e.handleClose(...t)),class:"iv-img",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAVUExURc7OzsfHx0dwTMfHx8fHx8bGxsbGxjm0J4cAAAAGdFJOUx7fANm68+QZZ9kAAADLSURBVDjLjZRLCsMwDEQnIQfIquugk2TTnqB0H2h1/yPUNL+RxJRkYQieJ0tjyRievZVvus94+Fg3On/j5UtBppt/GlGRzhvRlow0wEf81gIsPawgqxRWkFUJy8gmhGVk08ESsstgCdlVsIgcIlhEDg3i7ylBDHAGRYhAMRHOpCzAWXLe4Lq4UnBhXCjYCrYGbB6biWA32X9hQ4WSh6t0ZYHKEmmisl1elLpa2QyqfWTDqRaVTa3GQA6OGjU5nHKc5QPw58kQj4x6lr7CusSRCOifdQAAAABJRU5ErkJggg=="})])]),r("div",y,h(e.$t("Verify Your Identity")),1),r("div",C,[c(h(e.$t("Alternatively,"))+" ",1),r("span",{class:"upload-id-text",onClick:t[1]||(t[1]=(...t)=>e.hanldeUploadID&&e.hanldeUploadID(...t))},h(e.$t("upload your ID document")),1),c(" "+h(e.$t("to verify your identity.")),1)]),t[3]||(t[3]=r("div",{id:"greenid-div"},null,-1))])),[[g,e.loading]])}],["__scopeId","data-v-d7567240"]]);export{k as default};
