import{u as s,s as e}from"./unselected-C8BCzUVZ.js";import{_ as t}from"./index-M11nEPjl.js";import{J as n,K as i,S as o,F as a,Y as c,V as d}from"./vue-vendor-DjIN0JG5.js";const l={class:"questions-box"},r={class:"qnalist"},u={class:"question"},p={class:"answerlist"},m=["onClick"],v=["src"];const q=t({props:{questions:{type:Array,default:()=>[]},answers:{type:Array,default:()=>[]}},emits:["choose"],data:()=>({selected2:e,unselected:s}),methods:{isSelected(s,e){const t=this.answers.findIndex((e=>e.questionId===s));return this.answers[t]&&this.answers[t].answers2.includes(e)},choose(s,e){const t=s.questionId,n=e.id;this.$emit("choose",t,n)}}},[["render",function(s,e,t,q,h,w){return i(),n("div",l,[o("div",r,[(i(!0),n(a,null,c(t.questions,((s,e)=>(i(),n("div",{class:"qna",key:e},[o("div",u,"("+d(e+1)+") "+d(s.desc),1),o("div",p,[(i(!0),n(a,null,c(s.questionOptions,((e,t)=>(i(),n("div",{class:"answer",key:t},[o("div",{class:"row",onClick:t=>w.choose(s,e)},[o("img",{class:"selectImage",src:w.isSelected(s.questionId,e.id)?h.selected2:h.unselected,style:{"pointer-events":"auto"}},null,8,v),o("p",null,d(e.desc),1)],8,m)])))),128))])])))),128))])])}],["__scopeId","data-v-3016c416"]]);export{q as n};
