import{s as t,E as e,I as a,b as s}from"./vant-vendor-D8PsFlrJ.js";import{_ as i,a as n}from"./empty_light-BcIdMEkV.js";import{_ as l,a as r}from"./question_light-CQqO8HhY.js";import{n as o}from"./navbar-DfgFEjpa.js";import{s as c,C as d}from"./calendar-Cgct8rqI.js";import{s as m,r as h,_ as v,u}from"./index-M11nEPjl.js";import{g as p}from"./signalStmt-DaX-YCgN.js";import{I as g,J as w,K as y,j as _,S,L as f,V as k,M as $,W as C,F as L,Y as x,f as b}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const D={class:"container"},j=["src"],R={class:"content"},Y={class:"select-picker"},I={class:"select-picker-item-text"},P=["src"],M={key:1},T={class:"section-container"},U={class:"header-title"},O={class:"title"},q={class:"card"},A={class:"card-wrapper highlight"},B={class:"text"},E={class:"text right"},V={key:0,class:"card-wrapper"},N={class:"text"},W={class:"text right"},F={key:1,class:"card-wrapper"},z={class:"text"},J={class:"text right"},K={class:"card-wrapper"},G={class:"text"},H={class:"text right"},Q={class:"section-container"},X={class:"header-title"},Z={class:"title"},tt={class:"card-wrapper"},et={class:"text"},at={class:"text right"},st={class:"card-wrapper"},it={class:"text"},nt={class:"text right"},lt={class:"card-wrapper"},rt={class:"text"},ot={class:"text right"},ct={class:"card-wrapper highlight"},dt={class:"text"},mt={class:"text right"},ht={class:"action-sheet-content"},vt={class:"content-title"},ut={class:"content-row"},pt={class:"content-row-title"},gt={class:"content-row-txt"},wt={class:"content-row"},yt={class:"content-row-title"},_t={class:"content-row-txt"},St={class:"content-row"},ft={class:"content-row-title"},kt={class:"content-row-txt"},$t={class:"content-row"};const Ct=v({mixins:[{data(){return{calendarVisible:!1,selectedDate:this.$pinia.state.value.params.myDate||this.formatDate((new Date).getTime()),endDate:this.$pinia.state.value.params.myDate||this.formatDate((new Date).getTime()),viewSummary:null,loading:!1,modalShow:!1,skeletonRowWidth:["40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%"]}},components:{navBar:o,Calendar:d,slideActionSheet:c},methods:{handleSummaryModal(){this.modalShow=!0},handleNavBarClickLeft(){h("501")},handleNavBarClickRight(){h("220")},handleOpenDatePicker(){this.calendarVisible=!0},handleCalendarClose(){this.calendarVisible=!1},handleCalendarSelect(t){this.selectedDate=t,this.endDate=t,this.calendarVisible=!1,this.getSummary()},formatDate(t){const e=new Date(t);return`${e.getDate().toString().padStart(2,"0")}/${(e.getMonth()+1).toString().padStart(2,"0")}/${e.getFullYear()}`},getSummary(){this.loading=!0;const{accountId:e,strategyId:a}=this.$pinia.state.value.params;p({accountId:e,strategyId:a,date:this.selectedDate}).then((e=>{"200"===e.code?(this.viewSummary=e.data,this.selectedDate=e.data.settlementStart?e.data.settlementStart:this.selectedDate,this.endDate=e.data.settlementEnd?e.data.settlementEnd:this.selectedDate):(this.viewSummary=null,t(e.msg)),this.loading=!1})).catch((()=>{this.loading=!1}))},handleTouchStart(t){this.startY=t.touches[0].clientY},handleTouchMove(t){this.moveY=t.touches[0].clientY;const e=this.moveY-this.startY;e>0&&(this.translateY=e)},handleTouchEnd(){this.moveY-this.startY>100?this.modalShow=!1:this.$refs.actionSheetRef.$el.style.transform="translateY(0)"},handleModalClose(){this.modalShow=!1},handleLink(){this.modalShow=!1,this.$router.push({name:"profitSharingRules"}),m({code:"250",title:this.$t("profit_sharing_statement"),iconList:["CLOSE"]})}},mounted(){m({code:"250",title:this.$t("profit_sharing_statement"),iconList:["CUSTOMER"]}),this.getSummary()}}],setup(){const{paramsStore:t}=u();return{emptyImgUrl:b((()=>new URL(Object.assign({"../../assets/images/common/empty_dark.webp":n,"../../assets/images/common/empty_light.webp":i})[`../../assets/images/common/empty_${t.themeTxt}.webp`],import.meta.url).href)),questionImgUrl:b((()=>new URL(Object.assign({"./assets/images/question_dark.webp":r,"./assets/images/question_light.webp":l})[`./assets/images/question_${t.themeTxt}.webp`],import.meta.url).href))}}},[["render",function(t,i,n,l,r,o){const c=g("nav-bar"),d=g("calendar"),m=e,h=a,v=s,u=g("slide-action-sheet");return y(),w("div",D,[_(c,{"left-text":t.$t("profit_sharing_statement"),onClickLeft:t.handleNavBarClickLeft},{default:f((()=>[S("div",{class:"nav-bar_right",onClick:i[0]||(i[0]=(...e)=>t.handleNavBarClickRight&&t.handleNavBarClickRight(...e))},[S("img",{src:l.questionImgUrl,alt:""},null,8,j)])])),_:1},8,["left-text","onClickLeft"]),S("div",R,[_(d,{visible:t.calendarVisible,onClose:t.handleCalendarClose,onSelected:t.handleCalendarSelect},null,8,["visible","onClose","onSelected"]),S("div",Y,[S("div",{class:"select-picker-item",onClick:i[1]||(i[1]=(...e)=>t.handleOpenDatePicker&&t.handleOpenDatePicker(...e))},[S("span",I,k(t.selectedDate),1),i[4]||(i[4]=S("span",{class:"icon icon-arrow_down"},null,-1))])]),_(v,{class:"stmt-skeleton",row:t.skeletonRowWidth.length,loading:t.loading,"row-width":t.skeletonRowWidth},{default:f((()=>{var e;return[t.viewSummary&&(null==(e=t.viewSummary)?void 0:e.detailList)?(y(),w("div",M,[S("div",T,[S("div",U,[S("div",O,k(t.$t("summary")),1),_(h,{class:"icon",name:"info-o",onClick:t.handleSummaryModal},null,8,["onClick"])]),S("div",q,[S("div",A,[S("div",B,k(t.$t("current_periods_payout")),1),S("div",E,k(t.viewSummary.detailList[0].receivedProfit)+" "+k(t.viewSummary.detailList[0].currency),1)]),t.viewSummary.detailList[0].settlementAmount?(y(),w("div",V,[S("div",N,k(t.$t("paid_amount")),1),S("div",W,k(t.viewSummary.detailList[0].settlementAmount)+" "+k(t.viewSummary.detailList[0].currency),1)])):C("",!0),t.viewSummary.detailList[0].pendingProfit?(y(),w("div",F,[S("div",z,k(t.$t("unpaid_amount")),1),S("div",J,k(t.viewSummary.detailList[0].pendingProfit)+" "+k(t.viewSummary.detailList[0].currency),1)])):C("",!0),S("div",K,[S("div",G,k(t.$t("payment_account")),1),S("div",H,k("-1"!=t.viewSummary.detailList[0].receiveAccount&&t.viewSummary.detailList[0].receiveAccount?t.viewSummary.detailList[0].receiveAccount:t.$t("no_payment_record")),1)])])]),i[5]||(i[5]=S("div",{class:"section-line"},null,-1)),S("div",Q,[S("div",X,[S("div",Z,k(t.$t("breakdown")),1)]),(y(!0),w(L,null,x(t.viewSummary.detailList[0].breakdown,((e,a)=>(y(),w("div",{class:"card",key:a},[S("div",tt,[S("div",et,k(t.$t("copiers_amount")),1),S("div",at,k(e.followerCount||0),1)]),S("div",st,[S("div",it,k(t.$t("copiers_floating_profits")),1),S("div",nt,k(e.netProfit||0)+" "+k(t.viewSummary.detailList[0].currency),1)]),S("div",lt,[S("div",rt,k(t.$t("profit_sharing_ratio")),1),S("div",ot,k(100*parseFloat(e.profitSharePercentage)||0)+"% ",1)]),S("div",ct,[S("div",dt,k(t.$t("current_periods_payout")),1),S("div",mt,k(e.totalReceivedProfit||0)+" "+k(t.viewSummary.detailList[0].currency),1)])])))),128))]),i[6]||(i[6]=S("div",{class:"section-line"},null,-1))])):(y(),$(m,{key:0,class:"empty",description:t.$t("no_records_found"),"image-size":"80"},{image:f((()=>[S("img",{src:l.emptyImgUrl,alt:""},null,8,P)])),_:1},8,["description"]))]})),_:1},8,["row","loading","row-width"]),_(u,{show:t.modalShow,"onUpdate:show":i[3]||(i[3]=e=>t.modalShow=e),onClose:t.handleModalClose},{content:f((()=>[S("div",ht,[S("div",vt,k(t.$t("glossary")),1),S("div",ut,[S("div",pt,k(t.$t("current_periods_payout")),1),S("div",gt,k(t.$t("glossary_current_periods_payout")),1)]),S("div",wt,[S("div",yt,k(t.$t("unpaid_amount")),1),S("div",_t,k(t.$t("glossary_unpaid_amount")),1)]),S("div",St,[S("div",ft,k(t.$t("payment_account")),1),S("div",kt,k(t.$t("glossary_payment_account")),1)]),S("div",$t,[S("div",{class:"link",onClick:i[2]||(i[2]=(...e)=>t.handleLink&&t.handleLink(...e))},k(t.$t("learn_more")),1)])])])),_:1},8,["show","onClose"])])])}],["__scopeId","data-v-54bcb480"]]);export{Ct as default};
