import{_ as t,a as e,g as a,b as r,P as s,p as i,c as A,d as o}from"./index-M11nEPjl.js";import{J as n,K as l,s as c,W as d,v as h,S as p,V as u,I as y,M as m,X as g,L as C,j as v,y as f,F as D,Y as x,A as b}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";import"./vant-vendor-D8PsFlrJ.js";var _="AU";const w={class:"chart-content"},k={class:"chart",ref:"chartRef"},R={key:0,class:"empty-container"},E=["src"],B={key:0,class:"loader"},P={key:1};const T=t({props:{options:{type:Object,default:()=>({})}},data:()=>({chart:null,loading:!0,emptyImageSrc:""}),watch:{options:{handler(t){this.loading=!0,t.series[0].data.length&&(this.chart?this.updateChart():this.setChart(t)),this.loading=!1},deep:!0}},methods:{setChart(t){window.Highcharts&&(this.chart=window.Highcharts.chart(this.$refs.chartRef,{...t,lang:{decimalPoint:".",thousandsSep:","},accessibility:{enabled:!1}}))},updateChart(){this.chart.update(this.options)}},async mounted(){setTimeout((()=>{this.loading=!1}),1e4);const t=1===this.$route.query.theme?"dark":"light";this.emptyImageSrc=await function(t){switch(t){case"../../packages/au/assets/images/common/empty_dark":case"../../packages/au/assets/images/common/empty_dark.webp":return e((()=>import("./empty_dark-BVM0wdOH.js")),[]);case"../../packages/au/assets/images/common/empty_light":case"../../packages/au/assets/images/common/empty_light.webp":return e((()=>import("./empty_light-t-w1hjKQ.js")),[]);case"../../packages/mo/assets/images/common/empty_dark":case"../../packages/mo/assets/images/common/empty_dark.webp":return e((()=>import("./empty_dark-Bx5JUpq2.js")),[]);case"../../packages/mo/assets/images/common/empty_light":case"../../packages/mo/assets/images/common/empty_light.webp":return e((()=>import("./empty_light-Bx5JUpq2.js")),[]);case"../../packages/pu/assets/images/common/empty_dark":case"../../packages/pu/assets/images/common/empty_dark.webp":return e((()=>import("./empty_dark-DBatJzQE.js")),[]);case"../../packages/pu/assets/images/common/empty_light":case"../../packages/pu/assets/images/common/empty_light.webp":return e((()=>import("./empty_light-B9GMM89U.js")),[]);case"../../packages/um/assets/images/common/empty_dark":case"../../packages/um/assets/images/common/empty_dark.webp":return e((()=>import("./empty_dark-COAuTnFS.js")),[]);case"../../packages/um/assets/images/common/empty_light":case"../../packages/um/assets/images/common/empty_light.webp":return e((()=>import("./empty_light-CoHNen7j.js")),[]);case"../../packages/vjp/assets/images/common/empty_dark":case"../../packages/vjp/assets/images/common/empty_dark.webp":return e((()=>import("./empty_dark-CmkXjIY9.js")),[]);case"../../packages/vjp/assets/images/common/empty_light":case"../../packages/vjp/assets/images/common/empty_light.webp":return e((()=>import("./empty_light-N-ePkA6L.js")),[]);default:return new Promise((function(e,a){("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(a.bind(null,new Error("Unknown variable dynamic import: "+t)))}))}}(`../../packages/${String(_).toLowerCase()}/assets/images/common/empty_${t}.webp`)}},[["render",function(t,e,a,r,s,i){return l(),n("div",w,[c(p("div",k,null,512),[[h,s.chart]]),s.chart?d("",!0):(l(),n("div",R,[p("img",{class:"empty-img",src:s.emptyImageSrc.default,alt:""},null,8,E),s.loading?(l(),n("div",B,u(t.$t("loading")),1)):(l(),n("div",P,u(t.$t("No Records Found")),1))]))])}],["__scopeId","data-v-1a5af3db"]]);function I(){let t,e,r,s;return 1===(Number(a("theme"))||0)?(t="rgba(255, 255, 255, 0.12)",e="rgba(255, 255, 255, 0.38)",r="rgba(255, 255, 255, 0.87)",s="rgba(255, 255, 255, 0.60)"):(t="rgba(61, 61, 61, 0.12)",e="rgba(61, 61, 61, 0.45)",r="#3d3d3d",s="rgba(61, 61, 61, 0.65)"),{lineColor:t,labelColor:e,color:r,textColor2:s}}const $=t({components:{HeighCharts:T},props:{categories:{type:Array},seriesData:{type:Array},yAxisLabelUnit:{type:String,default:""},yAxisTickAmount:{type:Number,default:5},xAxisLabelStep:{type:Number,default:2},tooltipUnit:{type:String,default:""}},computed:{chartOptions(){const{lineColor:t,labelColor:e}=I(),a=this.$props.yAxisLabelUnit,r=this.$props.tooltipUnit;return{chart:{type:"column",spacing:[5,0,0,0],backgroundColor:"none"},title:"none",xAxis:{categories:this.$props.categories,labels:{style:{fontSize:".2667rem",color:e},step:this.$props.xAxisLabelStep},lineColor:"none"},yAxis:{title:"none",labels:{style:{fontSize:".2667rem",color:e},formatter(){return this.value+a}},tickAmount:this.$props.yAxisTickAmount,gridLineColor:t,gridLineWidth:.5},credits:{enabled:!1},legend:{enabled:!1},exporting:{enabled:!1},tooltip:{useHTML:!0,backgroundColor:"rgba(3, 72, 84, 0.95)",borderColor:"none",borderRadius:10,shadow:!1,style:{color:"#FFFFFF",fontSize:"12px"},formatter:function(){return`<div class="tooltip-content">\n                                    <div class="tooltip-content-top">${this.y}${a}&nbsp;${r}</div>                                    <div  class="tooltip-content-bottom"> ${this.x}</div>\n                                </div>`},positioner:function(t,e,a){return{x:a.plotX+this.chart.plotLeft-t/2,y:a.plotY+this.chart.plotTop-e-10}},hideDelay:0},plotOptions:{column:{pointWidth:"16",borderRadius:3,borderColor:"none",zones:[{value:0,color:"#E35728"},{color:"#00C79C"}]}},series:[{name:"Data",data:this.$props.seriesData}]}}}},[["render",function(t,e,a,r,s,i){const A=y("HeighCharts");return l(),m(A,{options:i.chartOptions},null,8,["options"])}]]),F={props:{yearList:{type:Array},selectedYear:{type:Number,default:(new Date).getFullYear()},code:Number},data(){return{arrowDown:new URL(Object.assign({"/src/packages/au/assets/images/arrow_down_dark.webp":"data:image/webp;base64,UklGRmoCAABXRUJQVlA4WAoAAAAwAAAAHQAAEQAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMewAAAC8dQAQQTzD/8z//BAGkuROI5H0O5GIoBDgEEIwCMG4ShgFMoIX+WGb6BQk7cuHSaIjov8K2bZsuddvJGdsAAAAAYO9vtqaPzA+TKRfzwQ+GTLFf8r8h9wz1nWH5U6FiBETJyYimkRM62TVEXdwUOkSODJQOQYufvnUqAAA=","/src/packages/au/assets/images/arrow_down_light.webp":"data:image/webp;base64,UklGRngCAABXRUJQVlA4WAoAAAAwAAAAHQAAEQAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMigAAAC8dQAQQV0AgCJEn/o/kBoEgRJ74PxICQYg88X8kBAKUQRboCY8E1BYMhbcFxkES/Qduatt2okvsUYGdHCtqZECHjjRzXQLvXQ0R/Tebtk3SVmP/WAcAAAAAW34zEVtw/3MSObkffBBymfkgZIrrRQh8KGEQWoQOp8fpaY0CGgVGgVXxKXnVk9kHAA=="})[`/src/packages/au/assets/images/arrow_down_${"0"===this.$route.query.theme?"light":"dark"}.webp`],import.meta.url).href}},methods:{select(){const t=this.yearList.length?this.yearList:[this.selectedYear];r(this.code,{yearList:t,selectedYear:this.$props.selectedYear})}}},O=["src"];const L=t(F,[["render",function(t,e,a,r,s,i){return l(),n("div",{class:"bridge-select",onClick:e[0]||(e[0]=(...t)=>i.select&&i.select(...t))},[p("span",null,u(a.selectedYear),1),p("img",{src:s.arrowDown,alt:""},null,8,O)])}],["__scopeId","data-v-8d5fbc29"]]),Y={props:{title:{type:String,default:""},tip:{type:Boolean,default:!0}},emits:["clickTip"]},S={class:"chart-wrapper"},U={class:"title-container"},N={class:"title-content"},M={class:"title"};const W=t(Y,[["render",function(t,e,a,r,s,i){return l(),n("div",S,[p("div",U,[p("div",N,[p("div",M,u(a.title),1),a.tip?(l(),n("div",{key:0,class:"icon-warning",onClick:e[0]||(e[0]=e=>t.$emit("clickTip"))})):d("",!0)]),a.tip?(l(),n("div",{key:0,class:"info",onClick:e[1]||(e[1]=e=>t.$emit("clickTip"))})):d("",!0),g(t.$slots,"title-right",{},void 0,!0)]),g(t.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-ff97f32f"]]),H={class:"chart-container"},Q={class:"footer-container"},V={class:"footer-item-l"},j={class:"footer-item-title"},X={class:"footer-item-r"},q={class:"footer-item-title"};const Z={class:"chart-container"},J={class:"max-drawdown"},G={class:"max-drawdown-box"},z={class:"max-drawdown-item"},K={class:"item-title"},tt={class:"item-value"},et={class:"max-drawdown-item"},at={class:"item-title"},rt={class:"item-value"},st={class:"max-drawdown-item"},it={class:"item-title"},At={class:"item-value"};const ot={class:"chart-container"},nt={class:"footer-box"},lt={class:"footer-item"},ct={class:"item-title"},dt={class:"item-value"},ht={class:"footer-item"},pt={class:"item-title"},ut={class:"copy-asset-under-management"},yt={class:"title"},mt={class:"chart-container"};const gt={class:"top-box"},Ct={class:"top-item"},vt={class:"item-title"},ft={class:"item-value"},Dt={class:"top-item"},xt={class:"item-title"},bt={class:"category-chart-wrapper"},_t={class:"chart-container-label"},wt={key:0,class:"frequently-traded"},kt={class:"card-title"},Rt={class:"frequently-traded-content"},Et={class:"th"},Bt={class:"td"},Pt={class:"td text-center"},Tt={class:"td text-right"},It={class:"td td-symbol"},$t={class:"td text-center td-percent"};const Ft={class:"others-content"},Ot={class:"title"},Lt={class:"value"};let Yt="https://stapp.vttechfx.com:16443/stTradeApp";const St=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Ut={class:"strategy-chart-container"};const Nt=t({components:{ReturnRateChart:t({components:{chartColumn:$,BridgeSelector:L,chartWrapper:W},props:{xAxisData:{type:Array},yAxisData:{type:Array},YTD:{type:String},MAX:{type:String},yearList:{type:Array},selectedYear:{type:Number}},data:()=>({POPUP_CODE:s}),methods:{setNumberClass:t=>"ltr "+(Number(t)>=0?"upper-number":"lower-number")}},[["render",function(t,e,a,r,s,i){const A=y("bridge-selector"),o=y("chartColumn"),n=y("chart-wrapper");return l(),m(n,{class:"return-rate-chart",title:t.$t("strategyChart.part1.title"),tip:!1},{"title-right":C((()=>[v(A,{selectedYear:a.selectedYear,yearList:a.yearList,code:s.POPUP_CODE.SELECT_YEAR_EVENT_1},null,8,["selectedYear","yearList","code"])])),default:C((()=>[p("div",H,[v(o,{categories:a.xAxisData,seriesData:a.yAxisData,yAxisLabelUnit:"%"},null,8,["categories","seriesData"])]),p("div",Q,[p("div",V,[p("div",j,u(t.$t("strategyChart.part1.YTD")),1),p("div",{class:f(["footer-item-value",i.setNumberClass(a.YTD)])},u(a.YTD)+"% ",3)]),p("div",X,[p("div",q,u(t.$t("strategyChart.part1.MAX")),1),p("div",{class:f(["footer-item-value",i.setNumberClass(a.MAX)])},u(a.MAX)+"% ",3)])])])),_:1},8,["title"])}],["__scopeId","data-v-0c857b89"]]),MonthlyRiskBandChart:t({components:{BridgeSelector:L,chartWrapper:W,chartColumn:$},props:{xAxisData:{type:Array},yAxisData:{type:Array},daily:{type:String,default:"0"},monthly:{type:String,default:"0"},yearly:{type:String,default:"0"},yearList:{type:Array},selectedYear:{type:Number}},data:()=>({POPUP_CODE:s}),methods:{popup:r}},[["render",function(t,e,a,r,s,i){const A=y("bridge-selector"),o=y("chart-column"),n=y("chart-wrapper");return l(),m(n,{onClickTip:e[1]||(e[1]=t=>i.popup(s.POPUP_CODE.MONTHLY_RISK_BAND)),class:"monthly-risk-band-chart",title:t.$t("strategyChart.part2.title")},{"title-right":C((()=>[v(A,{selectedYear:a.selectedYear,yearList:a.yearList,code:s.POPUP_CODE.SELECT_YEAR_EVENT_2},null,8,["selectedYear","yearList","code"])])),default:C((()=>[p("div",Z,[v(o,{categories:a.xAxisData,seriesData:a.yAxisData},null,8,["categories","seriesData"])]),p("div",null,[p("span",J,[p("span",null,u(t.$t("strategyChart.part2.max_drawdown")),1),p("div",{class:"icon-warning size",onClick:e[0]||(e[0]=t=>i.popup(s.POPUP_CODE.MAX_DRAWDOWN))})]),p("div",G,[p("div",z,[p("div",K,u(t.$t("strategyChart.part2.daily")),1),p("div",tt,u(a.daily),1)]),p("div",et,[p("div",at,u(t.$t("strategyChart.part2.monthly")),1),p("div",rt,u(a.monthly),1)]),p("div",st,[p("div",it,u(t.$t("strategyChart.part2.yearly")),1),p("div",At,u(a.yearly),1)])])])])),_:1},8,["title"])}],["__scopeId","data-v-a3d0a90b"]]),FollowerChart:t({components:{chartWrapper:W,ChartLine:t({components:{HeightChart:T},props:{categories:{type:Array},seriesData:{type:Array}},computed:{chartOptions(){const{labelColor:t,lineColor:e}=I();return{chart:{type:"areaspline",spacing:[5,0,0,0],backgroundColor:"none"},title:"none",credits:{enabled:!1},legend:{enabled:!1},exporting:{enabled:!1},xAxis:{categories:this.$props.categories,title:"none",labels:{style:{fontSize:".2667rem",color:t}},lineColor:"none"},yAxis:{title:"none",labels:{style:{fontSize:".2667rem",color:t}},gridLineColor:e,gridLineWidth:.5},tooltip:{useHTML:!0,backgroundColor:"rgba(3, 72, 84, 0.95)",borderColor:"none",borderRadius:10,style:{color:"#FFFFFF",fontSize:"12px"},shadow:!1,formatter:function(){return`<div class="tooltip-content">\n                                    <div class="tooltip-content-top">${this.y}</div>\n                                    <div  class="tooltip-content-bottom"> ${this.x}</div>\n                                </div>`},positioner:function(t,e,a){return{x:a.plotX+this.chart.plotLeft-t/2,y:a.plotY+this.chart.plotTop-e-10}},crosshairs:!0,hideDelay:0},plotOptions:{areaspline:{marker:{radius:0},lineWidth:1,color:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,"#00C79C"],[1,"rgba(0, 199, 156, 0)"]]},states:{hover:{lineWidth:1}}}},series:[{name:"",type:"areaspline",data:this.$props.seriesData}]}}}},[["render",function(t,e,a,r,s,i){const A=y("HeightChart");return l(),m(A,{options:i.chartOptions},null,8,["options"])}]]),chartColumn:$},props:{xAxisData:{type:Array},yAxisData:{type:Array},xAxisData2:{type:Array},yAxisData2:{type:Array},cumulativeCopiers:{type:Number,default:0},followerInSevenDays:{type:Number,default:0},followerAddNetPer:{type:String,default:"0"},tooltipUnit:{type:String,default:""}},data:()=>({POPUP_CODE:s}),methods:{setNumberClass:t=>"ltr "+(Number(t)>=0?"upper-number":"lower-number"),popup:r}},[["render",function(t,e,a,r,s,i){const A=y("chart-line"),o=y("chart-column"),n=y("chart-wrapper");return l(),m(n,{onClickTip:e[1]||(e[1]=t=>i.popup(s.POPUP_CODE.ACTIVE_COPIERS)),class:"follower-chart",title:t.$t("strategyChart.part3.title")},{default:C((()=>[p("div",ot,[v(A,{categories:a.xAxisData,seriesData:a.yAxisData},null,8,["categories","seriesData"])]),p("div",nt,[p("div",lt,[p("div",ct,u(t.$t("strategyChart.part3.cumulative_copiers")),1),p("div",dt,u(a.cumulativeCopiers),1)]),p("div",ht,[p("div",pt,u(t.$t("strategyChart.part3.copiers_last_7_days")),1),p("div",{class:f(["item-value",i.setNumberClass(a.followerInSevenDays)])},u(a.followerInSevenDays>=0?"+":"")+u(a.followerInSevenDays)+"("+u(a.followerAddNetPer)+"%) ",3)])]),c(p("div",ut,[p("div",yt,[p("span",null,u(t.$t("strategyChart.part3.copy_asset_under_management")),1),p("div",{class:"icon-warning size",onClick:e[0]||(e[0]=t=>i.popup(s.POPUP_CODE.COPY_ASSET_UNDER_MANAGEMENT))})]),p("div",mt,[v(o,{yAxisTickAmount:7,xAxisLabelStep:1,categories:a.xAxisData2,seriesData:a.yAxisData2,tooltipUnit:a.tooltipUnit},null,8,["categories","seriesData","tooltipUnit"])])],512),[[h,a.yAxisData2&&a.yAxisData2.length]])])),_:1},8,["title"])}],["__scopeId","data-v-b5157c14"]]),CategoryChart:t({components:{chartWrapper:W,ChartPie:t({components:{HeightChart:T},props:{seriesData:{type:Array}},methods:{updateSubtitle(t){const e=this.$refs.heightChartRef.chart;e&&e.update({subtitle:{text:`\n                        <div style="text-align: center">\n                            <div class="pie-center-value">${t?t.y+"%":""}</div>\n                            <div class="pie-center-title">${t?t.name:""}</div>\n                        </div>`}})}},computed:{chartOptions(){return{chart:{type:"pie",spacing:[0,0,0,0],backgroundColor:"none"},title:"none",credits:{enabled:!1},legend:{enabled:!1},exporting:{enabled:!1},subtitle:{useHTML:!0,text:"",floating:!0,verticalAlign:"middle"},tooltip:"none",plotOptions:{series:{borderRadius:0,borderWidth:2,cursor:"pointer",borderColor:"none",dataLabels:{enabled:!1},events:{click:({point:t})=>{this.updateSubtitle(t)}}}},series:[{animation:{duration:2e3},colorByPoint:!0,innerSize:"60%",data:this.$props.seriesData}],colors:["#007FFF","#E8E8E8","#FF8E5C","#FF3C70","#034854","#00C79C","#E35728","rgba(61, 61, 61, 0.45)"]}}}},[["render",function(t,e,a,r,s,i){const A=y("HeightChart");return l(),m(A,{options:i.chartOptions,ref:"heightChartRef"},null,8,["options"])}]])},props:{data:{type:Array,default:()=>[]},totalTrades:{type:Number,default:0},winRate:{type:String,default:"0"},frequentlyTraded:{type:Array,default:()=>[]}},data:()=>({pieItemColor:["#007FFF","#E8E8E8","#FF8E5C","#FF3C70","#034854","#00C79C","#E35728","rgba(61, 61, 61, 0.45)"],POPUP_CODE:s}),methods:{popup:r,setNumberClass:t=>"ltr "+(Number(t)>=0?"upper-number":"lower-number")}},[["render",function(t,e,a,r,s,i){const A=y("chart-pie"),o=y("chart-wrapper");return l(),m(o,{onClickTip:e[0]||(e[0]=t=>i.popup(s.POPUP_CODE.TRADES_STATS)),class:"category-chart",title:t.$t("strategyChart.part4.title")},{default:C((()=>[p("div",gt,[p("div",Ct,[p("div",vt,u(t.$t("strategyChart.part4.total_trades")),1),p("div",ft,u(a.totalTrades),1)]),p("div",Dt,[p("div",xt,u(t.$t("strategyChart.part4.win_rate")),1),p("div",{class:f(["item-value",i.setNumberClass(a.winRate)])},u(a.winRate)+"% ",3)])]),p("div",bt,[p("div",{class:f(["chart-container",!a.data.length&&"chart-container-empty"])},[v(A,{seriesData:a.data},null,8,["seriesData"])],2),p("div",_t,[(l(!0),n(D,null,x(a.data,((t,e)=>(l(),n("div",{class:"pie-label-item",key:e},[p("span",{class:"dot",style:b({background:s.pieItemColor[e]})},null,4),p("span",null,u(t.name),1),p("span",null," ("+u(Math.floor(t.trades))+") ",1)])))),128))])]),a.frequentlyTraded&&a.frequentlyTraded.length?(l(),n("div",wt,[p("div",kt,u(t.$t("strategyChart.part4.frequently_traded")),1),p("div",Rt,[p("div",Et,[p("div",Bt,u(t.$t("strategyChart.part4.symbol")),1),p("div",Pt,u(t.$t("strategyChart.part4.percentage")),1),p("div",Tt,u(t.$t("strategyChart.part4.win_rate")),1)]),(l(!0),n(D,null,x(a.frequentlyTraded,((t,e)=>{var a;return l(),n("div",{class:"tr",key:e},[p("div",It,u(t.product),1),p("div",$t,u((null==(a=Number(t.tradesPercent))?void 0:a.toFixed(2))||0)+"% ("+u(t.trades??"-")+") ",1),p("div",{class:f(["td text-right td-winRate",i.setNumberClass(t.profitableTradesPercent)])},u(Number(t.profitableTradesPercent).toFixed(2))+"% ",3)])})),128))])])):d("",!0)])),_:1},8,["title"])}],["__scopeId","data-v-4cc697b4"]]),Other:t({components:{chartWrapper:W},props:{data:{type:Object}},data:()=>({POPUP_CODE:s}),computed:{others(){return[{title:this.$t("strategyChart.part5.active_since"),value:this.$props.data.activeSince},{title:this.$t("strategyChart.part5.settlement_frequency"),value:this.$props.data.settlementCycle},{title:this.$t("strategyChart.part5.avg"),value:this.$props.data.averageHoldingHour},{title:this.$t("strategyChart.part5.trades_per_week"),value:this.$props.data.averageWeeklyTrades},{title:this.$t("strategyChart.part5.profitable_weeks"),value:this.$props.data.averageWeeklyProfitPercentage}]}},methods:{popup:r}},[["render",function(t,e,a,r,s,i){const A=y("chart-wrapper");return l(),m(A,{onClickTip:e[0]||(e[0]=t=>i.popup(s.POPUP_CODE.OTHERS)),class:"others card",title:t.$t("strategyChart.part5.title")},{default:C((()=>[p("div",Ft,[(l(!0),n(D,null,x(i.others,((t,e)=>(l(),n("div",{class:"others-item",key:e},[p("div",Ot,u(t.title),1),p("div",Lt,u(t.value),1)])))),128))])])),_:1},8,["title"])}],["__scopeId","data-v-e0952597"]])},mixins:[{data(){return{accountId:this.$route.query.strategyId,returnRateChart:{data:[],xAxisData:[],yAxisData:[],year:(new Date).getFullYear(),yearList:[],YTD:"0",MAX:"0"},monthlyRiskBandChart:{data:[],xAxisData:[],yAxisData:[],year:(new Date).getFullYear(),yearList:[],daily:"0",monthly:"0",yearly:"0"},followerChart:{xAxisData:[],yAxisData:[],cumulativeCopiers:0,copiersLast:0,followerAddNetPer:"0"},fundChart:{xAxisData:[],yAxisData:[],cumulativeCopiers:"0",copiersLast:"0",tooltipUnit:""},categoryChart:{data:[],totalTrades:0,winRate:"0",frequentlyTraded:[]},other:{activeSince:"—",settlementCycle:"—",averageHoldingHour:"—",averageWeeklyTrades:"—",averageWeeklyProfitPercentage:"—"},initFirstCharts:!1,initOthersCharts:!1}},methods:{async initReturnRateChart(){try{const{data:t}=await(t=>i(`${Yt}/social-trade/return-rate-chart/v2`,t))({accountId:this.accountId}),{monthlyReturnRateChartList:e,returnYTD:a,maxMonthlyReturn:r}=t;this.returnRateChart.data=e,this.returnRateChart.YTD=(100*a).toFixed(2),this.returnRateChart.MAX=(100*r).toFixed(2),this.returnRateChart.yearList=e.map((t=>t.year)),this.changeReturnChartYear(this.returnRateChart.year)}catch(t){this.returnRateChart.xAxisData=[],this.returnRateChart.yAxisData=[]}},changeReturnChartYear(t){this.returnRateChart.year=t;let e=this.returnRateChart.data.find((e=>e.year===t)).timePoints||[];if(null===e)return;e.length<12&&(e=e.concat(new Array(12-e.length).fill({value:"0",time:""})));let a=[],r=[];e.forEach(((t,e)=>{a.push(Number((100*t.value).toFixed(2))),r.push(St[e])})),this.returnRateChart.xAxisData=r,this.returnRateChart.yAxisData=a},async initMonthlyRiskBandChart(){try{const{data:t}=await(t=>i(`${Yt}/social-trade/monthly-riskband-chart`,t))({accountId:this.accountId}),{monthlyRiskbandChartList:e,maxDrawDownDay:a,maxDrawDownMonth:r,maxDrawDownYear:s}=t;this.monthlyRiskBandChart.data=e,this.monthlyRiskBandChart.daily=(100*a).toFixed(2)+"%",this.monthlyRiskBandChart.monthly=(100*r).toFixed(2)+"%",this.monthlyRiskBandChart.yearly=(100*s).toFixed(2)+"%",this.monthlyRiskBandChart.yearList=e.map((t=>t.year)),this.changeMonthlyRiskBandChartYear(this.monthlyRiskBandChart.year)}catch(t){this.monthlyRiskBandChart.xAxisData=[],this.monthlyRiskBandChart.yAxisData=[]}},changeMonthlyRiskBandChartYear(t){this.monthlyRiskBandChart.year=t;let e=this.monthlyRiskBandChart.data.find((e=>e.year===t)).timePoints;if(null===e)return;e.length<12&&(e=e.concat(new Array(12-e.length).fill({value:0,time:""})));const a=[],r=[];e.forEach(((t,e)=>{a.push(St[e]),r.push(Number(Number(t.value).toFixed(0)))})),this.monthlyRiskBandChart.xAxisData=a,this.monthlyRiskBandChart.yAxisData=r},async initFollowerChart(){try{const{data:t}=await(t=>i(`${Yt}/social-trade/follower-chart?accountId=${t}`,null))(this.accountId),{timePoints:e,followerInSevenDays:a,followerAddNetPer:r,allFollowerCount:s}=t;if(this.followerChart.cumulativeCopiers=s,this.followerChart.followerInSevenDays=Math.floor(a),this.followerChart.followerAddNetPer=(100*r).toFixed(2),e&&0===e.length)return;const A=[],o=[];e.forEach((t=>{const e=t.time.substring(0,t.time.lastIndexOf("/")),a=Number(t.value);A.push(e),o.push(a)})),this.followerChart.xAxisData=A,this.followerChart.yAxisData=o}catch(t){this.followerChart.xAxisData=[],this.followerChart.yAxisData=[]}},async initFundChart(){try{const{data:t}=await(({accountId:t,strategyId:e})=>A(`${Yt}/strategy/fund-chart-format-str?accountId=${t}&strategyId=${e}`,null))({accountId:this.$route.query.accountId||"",strategyId:this.$route.query.strategyId}),{timePoints:e,currency:a}=t;let r=e;r.length<7&&(r=r.concat(new Array(7-e.length).fill({value:0,time:""}))),this.fundChart.tooltipUnit=a;const s="JPY"===a||"USC"===a?0:2;r.forEach((t=>{this.fundChart.xAxisData.push(t.time.substring(0,t.time.lastIndexOf("/"))),this.fundChart.yAxisData.push(Number(Number(t.value).toFixed(s)))}))}catch(t){this.fundChart.xAxisData=[],this.fundChart.yAxisData=[]}},async initCategoryChart(){try{const{data:t}=await(t=>i(`${Yt}/social-trade/trade/category-chart?accountId=${t}`,null))(this.accountId),e=t;this.categoryChart.totalTrades=e.totalTrades,this.categoryChart.winRate=e.profitableTradesPercent;const a=[];for(const r in e){const t=e[r];t.hasOwnProperty("tradesPercent")&&t.tradesPercent>0&&a.push({y:t.tradesPercent,name:r.charAt(0).toUpperCase()+r.slice(1),trades:t.trades})}this.categoryChart.data=a,this.initProductChart()}catch(t){this.categoryChart.data=[]}},async initProductChart(){const{data:t}=await(t=>i(`${Yt}/social-trade/trade/product-chart?accountId=${t}`,null))(this.accountId);this.categoryChart.frequentlyTraded=t},async initOther(){try{const{data:t}=await(t=>i(`${Yt}/strategy/detail/others`,t))({strategyId:this.accountId});switch(t.settlementCycle){case 1:t.settlementCycle="Daily";break;case 2:t.settlementCycle="Weekly";break;case 3:t.settlementCycle="Monthly"}this.other=t}catch(t){}},initRegisterHandler(){o((({code:t,data:e})=>{6===t?this.changeReturnChartYear(Number(e.selectedYear)):7===t&&this.changeMonthlyRiskBandChartYear(Number(e.selectedYear))}))}},mounted(){setTimeout((()=>{this.initReturnRateChart()}),1e3),setTimeout((()=>{this.initMonthlyRiskBandChart(),this.initFollowerChart()}),2e3),setTimeout((()=>{this.initFundChart()}),3e3),setTimeout((()=>{this.initCategoryChart(),this.initOther(),this.initRegisterHandler()}),4e3)}}]},[["render",function(t,e,a,r,s,i){const A=y("ReturnRateChart"),o=y("MonthlyRiskBandChart"),c=y("FollowerChart"),d=y("CategoryChart"),h=y("Other");return l(),n("div",Ut,[v(A,{xAxisData:t.returnRateChart.xAxisData,yAxisData:t.returnRateChart.yAxisData,YTD:t.returnRateChart.YTD,MAX:t.returnRateChart.MAX,yearList:t.returnRateChart.yearList,selectedYear:t.returnRateChart.year},null,8,["xAxisData","yAxisData","YTD","MAX","yearList","selectedYear"]),v(o,{xAxisData:t.monthlyRiskBandChart.xAxisData,yAxisData:t.monthlyRiskBandChart.yAxisData,daily:t.monthlyRiskBandChart.daily,monthly:t.monthlyRiskBandChart.monthly,yearly:t.monthlyRiskBandChart.yearly,yearList:t.monthlyRiskBandChart.yearList,selectedYear:t.monthlyRiskBandChart.year},null,8,["xAxisData","yAxisData","daily","monthly","yearly","yearList","selectedYear"]),v(c,{xAxisData:t.followerChart.xAxisData,yAxisData:t.followerChart.yAxisData,cumulativeCopiers:t.followerChart.cumulativeCopiers,followerInSevenDays:t.followerChart.followerInSevenDays,followerAddNetPer:t.followerChart.followerAddNetPer,xAxisData2:t.fundChart.xAxisData,yAxisData2:t.fundChart.yAxisData,tooltipUnit:t.fundChart.tooltipUnit},null,8,["xAxisData","yAxisData","cumulativeCopiers","followerInSevenDays","followerAddNetPer","xAxisData2","yAxisData2","tooltipUnit"]),v(d,{data:t.categoryChart.data,winRate:t.categoryChart.winRate,totalTrades:t.categoryChart.totalTrades,frequentlyTraded:t.categoryChart.frequentlyTraded},null,8,["data","winRate","totalTrades","frequentlyTraded"]),v(h,{data:t.other},null,8,["data"])])}],["__scopeId","data-v-5daa715c"]]);export{Nt as default};
