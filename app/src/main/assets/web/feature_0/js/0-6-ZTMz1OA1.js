import{P as e,s as a}from"./vant-vendor-D8PsFlrJ.js";import{_ as t,e as i,r as s}from"./index-M11nEPjl.js";import{m as n,u as o,b as l,a as d,d as r,g as c}from"./index-BVEDVdSS.js";import{J as u}from"./vendor-CwRwASPO.js";import{w as m}from"./wrapperContainer-CL-3pf79.js";import{n as p}from"./questions-D-92lcJV.js";import{u as h,s as g}from"./unselected-C8BCzUVZ.js";import{_ as f}from"./remove_icon-CHjeUfRH.js";import{I as b,J as y,K as v,j as x,M as w,W as _,L as I,F as V,S as k,B as C,Y as F,A as j,V as M,y as q}from"./vue-vendor-DjIN0JG5.js";import"./navbar-DfgFEjpa.js";const L={class:"container"},P={class:"btn2 flex-center btn_upload"},S={id:"pic1",class:"flex-center"},U={class:"upload_files",id:"selectedFiles"},$={class:"row"},R=["id"],z=["onClick"],A={key:1,class:"read read_upload"},N={class:"read_field"},T=["src"],B={class:"selectImageSpan marginBottom0"},D={class:"row"},K={class:"label"},W={class:"w_100"},E={class:"bottom_btn"},Y={class:"modal_btn"},O={class:"modal_btn"},X={class:"modal_btn"};const J=t({components:{wrapperContainer:m,noQuestions:p},mixins:[n],computed:{imageSrc(){return this.readStatus?this.images.selected2:this.images.unselected}},data:()=>({images:{selected2:g,unselected:h},confirm:!1,readStatus:!1,sizeModalVisible:!1,fileExtensionModalVisible:!1,maxModalVisible:!1,imageFileList:[],textList:['I understand that Vantage Global Prime Pty Ltd ("Vantage"), ACN *********, AFSL No. 428901, trading as Vantage is the issuer of the products (Margin FX “FX” and Contracts-for Difference “CFDs”).',"I acknowledge that I have read, understood and agreed to be bound by Vantage’s Wholesale Client Information Statement , Wholesale Client Terms and Conditions(T&C’s), Client Categorisation Notice and Privacy Policy and Vantage FX Pty Ltd’s Privacy Policy.","I agree that I will not be acquiring financial products or services with Vantage in connection with a business.","I agree that Vantage may withdraw my status as a wholesale client at any time in its absolute discretion.","I acknowledge that the financial products and services which Vantage will provide to me as a wholesale client do not necessarily have the same investor protection and disclosure requirements as products made available to retail clients, and I am satisfied that I have the ability to obtain the information needed to make an informed decision.","I acknowledge and agree that upon submitting this application I may be requested to provide Vantage with additional information such as supporting evidence, additional or up to date Know-Your-Customer (KYC) information, as well as close any open position(s) I may currently hold under my existing account(s) before finalising my application.","Vantage has not given me a Product Disclosure Statement.","Vantage has not given me any other document that would be required to be given to me under Chapter 7 of the Corporations Act 2001 if the product or service were provided to me as a retail client.","Vantage does not have any other obligation to me under Chapter 7 of the Corporation Act 2991 that Vantage would have if the product or service were provided to me as a retail client.","If I am an existing Vantage client and have previously been treated as a retail client, I acknowledge that I will now be treated by Vantage as a wholesale clint, and that any previously issued disclosure and engagement documents will no longer correctly reflect my rights and powers and should not be relied on.","I understand and accept the risks associated with trading FX and CFD’s.","I consent to receive electronic communication from Vantage, including but not limited to trade confirmation statements and receipt of funds.","I confirm that the information provided by me and inserted in this form is correct and I acknowledge that I shall be obliged to inform Vantage if any of my information changes.","I agree that if there is any change relating to my wholesale client eligibility, I undertake to inform Vantage as soon as possible, but in any event, within 14 calendar days of such change in circumstances, and agree that Vantage shall not be liable for any losses arising from any delay or failure for me to notify Vantage.","I confirm that I have acted in my name as specified in this application and not on behalf of a third party in respect of all matters related to this client relationship. Accordingly, all funds to be deposited and traded on the account with Vantage are my funds.","I have read, understood, and agreed to be bound by Vantage’s deposits and withdrawals policy."]}),methods:{back(){s("501")},toggleReadStatus(){this.readStatus=!this.readStatus},async getUserAgreedTC(){this.loading=!0;const{data:e}=await this.useRequest(c,{userId:this.$pinia.state.value.params.userId,type:2});this.confirm=e.obj.confirm,this.loading=!1},async proclientProcess(){await this.useRequest(r,{userId:this.$pinia.state.value.params.userId,step:"0-6",statementFilePathList:this.imageFileList,isAgreedDeclaration:!0})},async handleNext(){if(this.confirm){if(0===this.imageFileList.length)return void a("Please upload images");await this.proclientProcess();const{data:e}=await this.useRequest(d,{userId:this.$pinia.state.value.params.userId});switch(e.obj.step){case"1-4":case"0-7":this.$router.push({path:"success",query:this.$route.query});break;default:this.$router.push({path:e.obj.step,query:this.$route.query})}}else{if(!this.readStatus)return void a("Please agree to the terms");await this.useRequest(l,{userId:this.$pinia.state.value.params.userId,choose:2,type:2}),this.confirm=!0}},upload(){this.$refs.inputFileUpload.click()},async imageFileCompressor(e){var a;let t=e.target.files[0].name.split(".").pop();if(e.target.files[0].size/1024>5e3)this.sizeModalVisible=!0;else if("png"!==t&&"jpg"!==t&&"jpeg"!==t)this.fileExtensionModalVisible=!0;else if(this.imageFileList.length>=6)this.maxModalVisible=!0;else{const t=(null==(a=this.$route.query)?void 0:a.accountId)||this.$route.query.userAccount,s=this;new u(e.target.files[0],{quality:.8,maxWidth:1600,maxHeight:1600,success(e){s.$refs.inputFileUpload.value="";let a=new FileReader;a.readAsDataURL(e),a.onloadend=async e=>{const a="TransRec_"+t+i()+".png",{data:n}=await s.uploadFile(e.target.result,a,a);s.imageFileList.push(n.obj.imgFile)}}})}},async uploadFile(e,a,t){return await this.useRequest(o,{token:this.$route.query.token,imgFile:a,imgBase64:e,imgName:t})},clearIndexImage(e){this.imageFileList.splice(e,1)}},created(){this.getUserAgreedTC()}},[["render",function(a,t,i,s,n,o){const l=b("noQuestions"),d=b("wrapper-container"),r=e;return v(),y("div",L,[x(d,{loading:a.loading},{default:I((()=>[a.loading?(v(),w(l,{key:0})):(v(),y(V,{key:1},[n.confirm?(v(),y(V,{key:0},[t[13]||(t[13]=k("div",{class:"contents upload_statement"},[k("div",{class:"text"},[C(" Based on what you have declared on your application, you "),k("span",{class:"text_bold"},"may be eligible"),C(" to be categorised as Wholesale client and be offered a PRO account – limited to Wholesale clients only. ")]),k("div",{class:"text"},[C(" To finalise your application, "),k("span",{class:"text_bold"},"Provide"),C(" us with a copy of your trading history to assess your trading experience. ")])],-1)),k("div",P,[k("label",S,[t[10]||(t[10]=C(" Upload Trading Statement ")),k("input",{type:"file",name:"imageUpload",id:"imageUpload",accept:".png, .jpg, .jpeg",ref:"inputFileUpload",onChange:t[0]||(t[0]=(...e)=>o.imageFileCompressor&&o.imageFileCompressor(...e))},null,544),t[11]||(t[11]=k("input",{type:"hidden",id:"imageUploadbase64Name"},null,-1))])]),t[14]||(t[14]=k("div",{class:"contents upload_statement"},[k("div",{class:"text text_bold mb_none"},"File size"),k("div",{class:"text"},[C(" The file exceeds the maximum upload size. "),k("br"),C(" Maximum upload size per file: 5MB ")]),k("div",{class:"text text_bold mb_none"}," Upload quantity "),k("div",{class:"text"}," You may only upload a maximum of 6 files. ")],-1)),k("div",U,[k("div",$,[(v(),y(V,null,F(6,((e,a)=>k("div",{class:"img_box",id:`upload_${a+1}`,key:a},[k("div",{class:"img_container",style:j(n.imageFileList[a]&&`background-image:url(${n.imageFileList[a]})`)},[n.imageFileList[a]?(v(),y("img",{key:1,src:f,class:"test",alt:"",onClick:e=>o.clearIndexImage(a)},null,8,z)):(v(),y("div",{key:0,class:"img_box_text box_upload",onClick:t[1]||(t[1]=(...e)=>o.upload&&o.upload(...e))},t[12]||(t[12]=[k("div",{class:"upload_plus"},null,-1),k("p",null,"Upload",-1)])))],4)],8,R))),64))])])],64)):(v(),y("div",A,[k("div",{class:"flex text read_contents",onClick:t[2]||(t[2]=(...e)=>o.toggleReadStatus&&o.toggleReadStatus(...e))},[k("div",N,[k("img",{src:o.imageSrc,alt:"status",class:"icon"},null,8,T)]),t[15]||(t[15]=k("p",{class:"read_field_label"},"By ticking this box:",-1))]),k("div",null,[k("span",B,[(v(!0),y(V,null,F(n.textList,((e,a)=>(v(),y("div",{class:"text",key:e},[k("div",D,[k("p",K,M(a),1),k("p",W,M(e),1)])])))),128))])])])),k("div",E,[k("div",{class:q(["btn flex-center btn_next",(n.readStatus||n.imageFileList.length&&n.confirm)&&"active"]),onClick:t[3]||(t[3]=(...e)=>o.handleNext&&o.handleNext(...e))}," Next ",2)])],64))])),_:1},8,["loading"]),a.loading?_("",!0):(v(),w(r,{key:0,show:n.sizeModalVisible,"onUpdate:show":t[5]||(t[5]=e=>n.sizeModalVisible=e)},{default:I((()=>[t[16]||(t[16]=k("div",{class:"modal_msg"},[k("p",{class:"mb_2"},"The file exceeds the maximum upload size."),k("p",null,"Maximum upload size per file: 5MB")],-1)),k("div",Y,[k("div",{class:"btn flex-center btn_next",onClick:t[4]||(t[4]=e=>n.sizeModalVisible=!1)}," OK ")])])),_:1},8,["show"])),a.loading?_("",!0):(v(),w(r,{key:1,show:n.fileExtensionModalVisible,"onUpdate:show":t[7]||(t[7]=e=>n.fileExtensionModalVisible=e)},{default:I((()=>[t[17]||(t[17]=k("div",{class:"modal_msg"},[k("p",{class:"mb_2"},"Please upload .png, .jpg, .jpeg file")],-1)),k("div",O,[k("div",{class:"btn flex-center btn_next",onClick:t[6]||(t[6]=e=>n.fileExtensionModalVisible=!1)}," OK ")])])),_:1},8,["show"])),a.loading?_("",!0):(v(),w(r,{key:2,show:n.maxModalVisible,"onUpdate:show":t[9]||(t[9]=e=>n.maxModalVisible=e)},{default:I((()=>[t[18]||(t[18]=k("div",{class:"modal_msg"},[k("p",null,"You may only upload a maximum of 6 files.")],-1)),k("div",X,[k("div",{class:"btn flex-center btn_next",onClick:t[8]||(t[8]=e=>n.maxModalVisible=!1)}," OK ")])])),_:1},8,["show"]))])}],["__scopeId","data-v-c7fff817"]]);export{J as default};
