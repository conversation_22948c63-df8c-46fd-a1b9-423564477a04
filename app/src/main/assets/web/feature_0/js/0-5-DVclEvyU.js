import{P as s}from"./vant-vendor-D8PsFlrJ.js";import{m as t,a as e,d as a,i,h as o}from"./index-BVEDVdSS.js";import{n}from"./questions-D-92lcJV.js";import{F as r}from"./failedModal-DlAunQaU.js";import{w as u}from"./wrapperContainer-CL-3pf79.js";import{_ as d}from"./index-M11nEPjl.js";import{I as l,M as c,K as h,L as p,j as m,S as w,y as g,V as f}from"./vue-vendor-DjIN0JG5.js";import"./unselected-C8BCzUVZ.js";import"./navbar-DfgFEjpa.js";import"./vendor-CwRwASPO.js";const v={class:"modal_msg space_2"};const q=d({mixins:[t],components:{wrapperContainer:u,Questions:n,FailedModal:r},data:()=>({questions:[],answers:[],show:!1,tryAgainModal:!1,quizSuccessModal:!1,number_test:1}),methods:{async toCheckViolation(s=!1){if(!this.$pinia.state.value.params.userId)return void this.login();const{data:t}=await this.useRequest(o,{userId:this.$pinia.state.value.params.userId});if(t.obj.isNormal)if(s)switch(t.obj.answerStep){case 1:this.number_test=1,this.tryAgainModal=!0;break;case 2:this.number_test=2,this.tryAgainModal=!0}else this.getQuestions();else this.show=!0},async getQuestions(){const{data:s}=await this.useRequest(i,{userId:this.$pinia.state.value.params.userId});this.questions=s.obj},choose(s,t){const e=this.answers.findIndex((t=>t.questionId===s));-1===e?this.answers.push({questionId:s,answers2:[t]}):this.answers[e].answers2=[t]},async next(){if(this.answers.length!==this.questions.length)return;const{data:s}=await this.useRequest(a,{userId:this.$pinia.state.value.params.userId,answers:this.answers,step:"0-5"});!1===s.obj.correct?this.toCheckViolation(!0):this.quizSuccessModal=!0},tryAgain(){this.tryAgainModal=!1,this.answers=[],this.getQuestions()},async handleSuccess(){const{data:s}=await this.useRequest(e,{userId:this.$pinia.state.value.params.userId});if(""!==s.obj.step)switch(s.obj.step){case"1-4":case"0-7":this.$router.push({path:"success",query:this.$route.query});break;default:this.$router.push({path:s.obj.step,query:this.$route.query})}}},created(){this.toCheckViolation()}},[["render",function(t,e,a,i,o,n){const r=l("questions"),u=l("FailedModal"),d=s,q=l("wrapper-container");return h(),c(q,{loading:t.loading,class:"question-page"},{default:p((()=>[m(r,{answers:o.answers,questions:o.questions,onChoose:n.choose},null,8,["answers","questions","onChoose"]),w("div",{class:"bottom_btn",onClick:e[0]||(e[0]=(...s)=>n.next&&n.next(...s))},[w("div",{class:g(["btn flex-center btn_next",o.answers.length===o.questions.length&&"active"])}," Next ",2)]),m(u,{value:o.show,"onUpdate:value":e[1]||(e[1]=s=>o.show=s)},null,8,["value"]),m(d,{show:o.tryAgainModal,"onUpdate:show":e[3]||(e[3]=s=>o.tryAgainModal=s)},{default:p((()=>[w("div",v,[e[6]||(e[6]=w("p",{class:"mb_2"}," We regret to inform you that you were unsuccessful in your attempt. ",-1)),w("p",null,' Click on "Try again" for '+f(2===o.number_test?"last":"second")+" attempt on Sophisticated Investor Quiz. ",1)]),w("div",{class:"button",onClick:e[2]||(e[2]=(...s)=>n.tryAgain&&n.tryAgain(...s))},"Try again")])),_:1},8,["show"]),m(d,{show:o.quizSuccessModal,"onUpdate:show":e[5]||(e[5]=s=>o.quizSuccessModal=s)},{default:p((()=>[e[7]||(e[7]=w("div",{class:"modal_msg space_3"},[w("p",{class:"mb_2"},"Congratulations!"),w("p",null," Your attempt on Sophicticated Investor Quiz is successful. ")],-1)),w("div",{class:"button",onClick:e[4]||(e[4]=(...s)=>n.handleSuccess&&n.handleSuccess(...s))},"Next")])),_:1},8,["show"])])),_:1},8,["loading"])}],["__scopeId","data-v-6c361ddd"]]);export{q as default};
