const t={props:{symbolList:{type:Array,default:()=>[]}},data(){return{show:!1,searchKey:"",list:this.symbolList}},watch:{searchKey:{handler(t){t?(this.show=!0,this.list=this.filterKey(t)):this.show=!1},immediate:!0}},computed:{copySymbolList(){return this.symbolList&&this.symbolList.length?JSON.parse(JSON.stringify(this.symbolList)):[]}},methods:{checkIncludeStr:(t,s)=>t.includes(s),filterKey(t){const s=[];return this.copySymbolList.forEach((e=>{let{symbol:i}=e,h=t.toUpperCase();i=i.toUpperCase(),this.checkIncludeStr(i,h)&&s.push(e)})),s.length?s:[]},goto(t){this.show=!1,this.$emit("gotoDetail",t)}}};export{t as s};
