package cn.com.vau.common.mvvm.ext

import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.util.LogUtil

/**
 * BatchRequestNet 使用示例
 * 展示如何使用新的并行网络请求方法
 */
class BatchRequestNetExample : BaseViewModel() {

    /**
     * 示例1：使用List方式调用多个接口
     */
    fun exampleBatchRequestWithList() {
        val requests = listOf<suspend () -> ApiResponse<Any>>(
            // 请求1：获取用户信息
            {
                val userParams = hashMapOf<String, Any?>(
                    "userId" to "12345",
                    "token" to "user_token"
                )
                baseService.accountHomeBaseApi(userParams)
            },
            // 请求2：获取交易数据
            {
                val tradeParams = hashMapOf<String, Any?>(
                    "accountId" to "67890",
                    "type" to "trading"
                )
                tradingService.getPositionList(tradeParams)
            },
            // 请求3：获取其他数据
            {
                val otherParams = hashMapOf<String, String>(
                    "category" to "news",
                    "limit" to "10"
                )
                baseService.getAccountSelectApi(otherParams)
            }
        )

        batchRequestNet(
            requests = requests,
            onComplete = { summary ->
                LogUtil.d("BatchRequest", "总请求数: ${summary.totalCount}")
                LogUtil.d("BatchRequest", "成功数: ${summary.successCount}")
                LogUtil.d("BatchRequest", "失败数: ${summary.failureCount}")
                LogUtil.d("BatchRequest", "是否全部成功: ${summary.isAllSuccess}")

                // 处理成功的结果
                summary.successResults.forEach { result ->
                    LogUtil.d("BatchRequest", "请求${result.requestIndex}成功: ${result.data}")
                    result.data?.let { response ->
                        if (response.isSuccess()) {
                            // 处理具体的成功数据
                            when (result.requestIndex) {
                                0 -> handleUserData(response)
                                1 -> handleTradeData(response)
                                2 -> handleOtherData(response)
                            }
                        }
                    }
                }

                // 处理失败的结果
                summary.failureResults.forEach { result ->
                    LogUtil.e("BatchRequest", "请求${result.requestIndex}失败: ${result.error?.message}")
                }

                // 根据业务需求决定如何处理混合结果
                if (summary.isAllSuccess) {
                    // 所有请求都成功
                    LogUtil.d("BatchRequest", "所有请求都成功，可以进行下一步操作")
                } else if (summary.hasAnySuccess) {
                    // 部分成功
                    LogUtil.d("BatchRequest", "部分请求成功，根据业务逻辑处理")
                } else {
                    // 全部失败
                    LogUtil.e("BatchRequest", "所有请求都失败了")
                }
            },
            onError = { throwable ->
                LogUtil.e("BatchRequest", "批量请求执行异常: ${throwable.message}")
            },
            isShowDialog = true,
            isAutoDismissDialog = true
        )
    }

    /**
     * 示例2：使用可变参数方式调用多个接口
     */
    fun exampleBatchRequestWithVarargs() {
        batchRequestNet<ApiResponse<Any>>(
            onComplete = { summary ->
                // 处理结果的逻辑与示例1相同
                processBatchResults(summary)
            },
            onError = { throwable ->
                LogUtil.e("BatchRequest", "批量请求执行异常: ${throwable.message}")
            },
            isShowDialog = true,
            // 直接传入多个请求
            {
                val params1 = hashMapOf<String, Any?>("userId" to "12345")
                baseService.accountHomeBaseApi(params1)
            },
            {
                val params2 = hashMapOf<String, Any?>("accountId" to "67890")
                tradingService.getPositionList(params2)
            },
            {
                val params3 = hashMapOf<String, String>("category" to "news")
                baseService.getAccountSelectApi(params3)
            }
        )
    }

    /**
     * 示例3：处理不同类型的响应
     */
    fun exampleMixedResponseTypes() {
        // 注意：如果需要处理不同类型的响应，建议使用 Any 作为泛型类型
        // 然后在处理结果时进行类型判断
        val mixedRequests = listOf<suspend () -> Any>(
            {
                val params = hashMapOf<String, Any?>("userId" to "12345")
                baseService.accountHomeBaseApi(params) // 返回 ApiResponse<UserData>
            },
            {
                val params = hashMapOf<String, String>("category" to "news")
                baseService.getAccountSelectApi(params) // 返回 ApiResponse<NewsData>
            }
        )

        batchRequestNet(
            requests = mixedRequests,
            onComplete = { summary ->
                summary.successResults.forEach { result ->
                    when (val data = result.data) {
                        is ApiResponse<*> -> {
                            // 根据请求索引或其他方式判断具体类型
                            LogUtil.d("BatchRequest", "收到ApiResponse: ${data.getResponseCode()}")
                        }
                        else -> {
                            LogUtil.d("BatchRequest", "收到其他类型数据: $data")
                        }
                    }
                }
            }
        )
    }

    // 辅助方法：处理批量请求结果
    private fun processBatchResults(summary: BatchRequestSummary<ApiResponse<Any>>) {
        LogUtil.d("BatchRequest", "处理批量请求结果...")
        
        // 可以根据业务需求实现具体的合并逻辑
        val allSuccessData = summary.successResults.mapNotNull { it.data?.getResponseData() }
        LogUtil.d("BatchRequest", "合并后的成功数据数量: ${allSuccessData.size}")
        
        // 这里可以实现具体的数据合并逻辑
        // 例如：合并用户信息和交易数据，生成完整的用户画像等
    }

    // 处理具体业务数据的示例方法
    private fun handleUserData(response: ApiResponse<Any>) {
        LogUtil.d("BatchRequest", "处理用户数据")
        // 具体的用户数据处理逻辑
    }

    private fun handleTradeData(response: ApiResponse<Any>) {
        LogUtil.d("BatchRequest", "处理交易数据")
        // 具体的交易数据处理逻辑
    }

    private fun handleOtherData(response: ApiResponse<Any>) {
        LogUtil.d("BatchRequest", "处理其他数据")
        // 具体的其他数据处理逻辑
    }
}
