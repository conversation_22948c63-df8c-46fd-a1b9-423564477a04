package cn.com.vau.common.mvvm.ext

import androidx.lifecycle.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ExceptionHandle
import cn.com.vau.util.LogUtil
import kotlinx.coroutines.*

/**
 * 不过滤请求结果
 * @param block 请求体 必须要用suspend关键字修饰
 * @param onSuccess 成功回调
 * @param onError 失败回调，如网络异常、解析异常等
 * @param isShowDialog 是否显示加载框，也可在VM层或V层showLoading()进行显示
 * @param isAutoDismissDialog 请求成功后是否关闭加载框，根据实际业务逻辑配置，也可在VM层或V层hideLoading()进行关闭
 *
 * 20241213：如果在代码里自己手动调用了 showLoadDialog()，请自行处理关闭弹框逻辑 hideLoadDialog()。把自己业务逻辑弹框和网络框架弹框解耦。
 *
 */
fun <T> BaseViewModel.requestNet(
    block: suspend () -> T,
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit = {},
    isShowDialog: Boolean = false,
    isAutoDismissDialog: Boolean = true
): Job {
    // 如果需要弹窗 通知Activity/fragment弹窗
    if (isShowDialog) loadingChange.dialogLiveData.postValue(true)
    return viewModelScope.launch {
        runCatching {
            // 请求体
            block()
        }.onSuccess {
            if (isAutoDismissDialog) { // 接口请求成功自动关闭loading框
                loadingChange.dialogLiveData.postValue(false)
            }
            /*
            1.接口请求成功，需要开发者自己判断code码是否正确，isSuccess()为成功，其他为失败
            2.如果开发者接口返回的实体不是ApiResponse<T>，则需要自己去处理
             */
            onSuccess(it)
        }.onFailure {
            // 请求异常 关闭弹窗
            loadingChange.dialogLiveData.postValue(false)
            // 打印错误栈信息
            it.printStackTrace()
            // 处理异常，吐司等操作
            val ex = ExceptionHandle.handleException(it)
            LogUtil.e("errMsg -> ${ex.throwable?.message}")
            // 把异常回调给开发者，供开发者做一些业务处理
            onError(it)
        }
    }
}

/**
 * AndroidViewModel级别的网络请求（需要自己处理loading框逻辑）
 */
fun <T> AndroidViewModel.requestNet(
    block: suspend () -> T,
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit = {},
): Job {
    return viewModelScope.launch {
        runCatching {
            // 请求体
            withContext(Dispatchers.IO) {
                block()
            }
        }.onSuccess {
            /*
            1.接口请求成功，需要开发者自己判断code码是否正确，isSuccess()为成功，其他为失败
            2.如果开发者接口返回的实体不是ApiResponse<T>，则需要自己去处理
             */
            onSuccess(it)
        }.onFailure {
            // 打印错误栈信息
            it.printStackTrace()
            // 处理异常，吐司等操作
            val ex = ExceptionHandle.handleException(it)
            LogUtil.e("errMsg -> ${ex.throwable?.message}")
            // 把异常回调给开发者，供开发者做一些业务处理
            onError(it)
        }
    }
}

/**
 * 批量网络请求结果数据类
 * @param T 请求结果类型
 */
data class BatchRequestResult<T>(
    val isSuccess: Boolean,
    val data: T? = null,
    val error: Throwable? = null,
    val requestIndex: Int = -1
)

/**
 * 批量网络请求汇总结果
 */
data class BatchRequestSummary<T>(
    val successResults: List<BatchRequestResult<T>>,
    val failureResults: List<BatchRequestResult<T>>,
    val allResults: List<BatchRequestResult<T>>
) {
    val isAllSuccess: Boolean get() = failureResults.isEmpty()
    val hasAnySuccess: Boolean get() = successResults.isNotEmpty()
    val successCount: Int get() = successResults.size
    val failureCount: Int get() = failureResults.size
    val totalCount: Int get() = allResults.size
}

/**
 * 并行执行多个网络请求，等待所有请求完成后处理合并的响应体
 * @param requests 请求体列表，每个都必须用suspend关键字修饰
 * @param onComplete 所有请求完成后的回调，包含成功和失败的结果汇总
 * @param onError 整体执行异常回调（非单个请求失败）
 * @param isShowDialog 是否显示加载框
 * @param isAutoDismissDialog 请求完成后是否关闭加载框
 */
fun <T> BaseViewModel.batchRequestNet(
    requests: List<suspend () -> T>,
    onComplete: (BatchRequestSummary<T>) -> Unit,
    onError: (Throwable) -> Unit = {},
    isShowDialog: Boolean = false,
    isAutoDismissDialog: Boolean = true
): Job {
    // 如果需要弹窗 通知Activity/fragment弹窗
    if (isShowDialog) loadingChange.dialogLiveData.postValue(true)

    return viewModelScope.launch {
        try {
            // 使用async并行执行所有请求
            val deferredResults = requests.mapIndexed { index, request ->
                async {
                    runCatching {
                        request()
                    }.fold(
                        onSuccess = { result ->
                            BatchRequestResult(
                                isSuccess = true,
                                data = result,
                                requestIndex = index
                            )
                        },
                        onFailure = { throwable ->
                            // 打印单个请求的错误信息
                            throwable.printStackTrace()
                            val ex = ExceptionHandle.handleException(throwable)
                            LogUtil.e("batchRequestNet -> Request $index failed: ${ex.throwable?.message}")

                            BatchRequestResult<T>(
                                isSuccess = false,
                                error = throwable,
                                requestIndex = index
                            )
                        }
                    )
                }
            }

            // 等待所有请求完成
            val allResults = deferredResults.awaitAll()

            // 分类结果
            val successResults = allResults.filter { it.isSuccess }
            val failureResults = allResults.filter { !it.isSuccess }

            // 创建汇总结果
            val summary = BatchRequestSummary(
                successResults = successResults,
                failureResults = failureResults,
                allResults = allResults
            )

            if (isAutoDismissDialog) {
                loadingChange.dialogLiveData.postValue(false)
            }

            // 回调汇总结果
            onComplete(summary)

        } catch (e: Exception) {
            // 整体执行异常（非单个请求失败）
            loadingChange.dialogLiveData.postValue(false)
            e.printStackTrace()
            val ex = ExceptionHandle.handleException(e)
            LogUtil.e("batchRequestNet -> Batch execution failed: ${ex.throwable?.message}")
            onError(e)
        }
    }
}

/**
 * 并行执行多个网络请求的简化版本（使用可变参数）
 * @param onComplete 所有请求完成后的回调
 * @param onError 整体执行异常回调
 * @param isShowDialog 是否显示加载框
 * @param isAutoDismissDialog 请求完成后是否关闭加载框
 * @param requests 可变参数的请求体
 */
fun <T> BaseViewModel.batchRequestNet(
    onComplete: (BatchRequestSummary<T>) -> Unit,
    onError: (Throwable) -> Unit = {},
    isShowDialog: Boolean = false,
    isAutoDismissDialog: Boolean = true,
    vararg requests: suspend () -> T
): Job {
    return batchRequestNet(
        requests = requests.toList(),
        onComplete = onComplete,
        onError = onError,
        isShowDialog = isShowDialog,
        isAutoDismissDialog = isAutoDismissDialog
    )
}