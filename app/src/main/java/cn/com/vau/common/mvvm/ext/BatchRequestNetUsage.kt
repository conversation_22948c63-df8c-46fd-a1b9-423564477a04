package cn.com.vau.common.mvvm.ext

import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.util.LogUtil
import cn.com.vau.util.UserDataUtil

/**
 * BatchRequestNet 实际使用示例
 * 展示在真实业务场景中如何使用并行网络请求
 */
class BatchRequestNetUsage : BaseViewModel() {

    /**
     * 实际业务场景：同时获取用户基础信息、交易数据和账户信息
     */
    fun loadUserCompleteData() {
        // 准备请求参数
        val userId = UserDataUtil.userId()
        val accountId = UserDataUtil.accountCd()
        val token = UserDataUtil.loginToken()

        batchRequestNet<ApiResponse<Any>>(
            onComplete = { summary ->
                LogUtil.d("UserData", "用户数据加载完成 - 成功:${summary.successCount}, 失败:${summary.failureCount}")
                
                // 处理成功的请求结果
                summary.successResults.forEach { result ->
                    result.data?.let { response ->
                        if (response.isSuccess()) {
                            when (result.requestIndex) {
                                0 -> {
                                    // 处理用户基础信息
                                    LogUtil.d("UserData", "用户基础信息加载成功")
                                    // 这里可以更新UI或保存数据
                                }
                                1 -> {
                                    // 处理交易数据
                                    LogUtil.d("UserData", "交易数据加载成功")
                                    // 更新交易相关UI
                                }
                                2 -> {
                                    // 处理账户选择数据
                                    LogUtil.d("UserData", "账户数据加载成功")
                                    // 更新账户相关UI
                                }
                            }
                        } else {
                            LogUtil.e("UserData", "请求${result.requestIndex}业务失败: ${response.getResponseMsg()}")
                        }
                    }
                }

                // 处理失败的请求
                summary.failureResults.forEach { result ->
                    LogUtil.e("UserData", "请求${result.requestIndex}网络失败: ${result.error?.message}")
                }

                // 根据成功情况决定后续操作
                when {
                    summary.isAllSuccess -> {
                        LogUtil.d("UserData", "所有数据加载成功，可以显示完整界面")
                        // 显示完整的用户界面
                    }
                    summary.hasAnySuccess -> {
                        LogUtil.d("UserData", "部分数据加载成功，显示可用数据")
                        // 显示部分数据，对失败的部分显示重试按钮
                    }
                    else -> {
                        LogUtil.e("UserData", "所有数据加载失败")
                        // 显示错误页面或重试按钮
                    }
                }
            },
            onError = { throwable ->
                LogUtil.e("UserData", "批量请求执行异常: ${throwable.message}")
                // 显示通用错误提示
            },
            isShowDialog = true,
            isAutoDismissDialog = true,
            // 请求1：获取用户基础信息
            {
                val userParams = hashMapOf<String, Any?>(
                    "userId" to userId,
                    "mt4AccountId" to accountId,
                    "token" to token,
                    "apkType" to "android"
                )
                // 根据账户类型设置参数
                when (UserDataUtil.accountDealType()) {
                    "3" -> userParams["isDemoAccount"] = "0"
                    "1" -> userParams["isDemoAccount"] = "1"
                    "6" -> userParams["isDemoAccount"] = "4"
                }
                baseService.accountHomeBaseApi(userParams)
            },
            // 请求2：获取交易持仓数据
            {
                val tradeParams = hashMapOf<String, Any?>(
                    "userId" to userId,
                    "accountId" to accountId,
                    "token" to token
                )
                tradingService.getPositionList(tradeParams)
            },
            // 请求3：获取账户选择列表
            {
                val accountParams = hashMapOf<String, String>(
                    "userId" to userId.toString(),
                    "token" to token.toString()
                )
                baseService.getAccountSelectApi(accountParams)
            }
        )
    }

    /**
     * 另一个业务场景：刷新多个模块的数据
     */
    fun refreshAllModuleData() {
        val requests = listOf<suspend () -> ApiResponse<Any>>(
            // 刷新新闻数据
            {
                val newsParams = hashMapOf<String, String>(
                    "category" to "financial",
                    "limit" to "20"
                )
                baseService.getAccountSelectApi(newsParams)
            },
            // 刷新市场数据
            {
                val marketParams = hashMapOf<String, Any?>(
                    "userId" to UserDataUtil.userId(),
                    "type" to "market"
                )
                baseService.accountHomeBaseApi(marketParams)
            }
        )

        batchRequestNet(
            requests = requests,
            onComplete = { summary ->
                LogUtil.d("Refresh", "数据刷新完成")
                
                // 可以根据需要实现更复杂的合并逻辑
                val allData = mutableListOf<Any>()
                summary.successResults.forEach { result ->
                    result.data?.getResponseData()?.let { data ->
                        allData.add(data)
                    }
                }
                
                LogUtil.d("Refresh", "合并后的数据数量: ${allData.size}")
                // 这里可以将合并后的数据发送给UI层
            },
            onError = { throwable ->
                LogUtil.e("Refresh", "刷新失败: ${throwable.message}")
            },
            isShowDialog = false, // 刷新时不显示loading
            isAutoDismissDialog = true
        )
    }

    /**
     * 错误处理示例：部分成功的情况下如何处理
     */
    fun handlePartialSuccess() {
        batchRequestNet<ApiResponse<Any>>(
            onComplete = { summary ->
                when {
                    summary.isAllSuccess -> {
                        // 全部成功的处理逻辑
                        LogUtil.d("Batch", "所有请求成功")
                    }
                    summary.hasAnySuccess -> {
                        // 部分成功的处理逻辑
                        LogUtil.d("Batch", "部分请求成功，成功率: ${summary.successCount}/${summary.totalCount}")
                        
                        // 可以选择性地处理成功的数据
                        summary.successResults.forEach { result ->
                            // 处理成功的数据
                            LogUtil.d("Batch", "处理成功的请求${result.requestIndex}")
                        }
                        
                        // 对失败的请求进行重试或显示错误信息
                        summary.failureResults.forEach { result ->
                            LogUtil.e("Batch", "请求${result.requestIndex}失败，可以考虑重试")
                        }
                    }
                    else -> {
                        // 全部失败的处理逻辑
                        LogUtil.e("Batch", "所有请求都失败了")
                    }
                }
            },
            isShowDialog = true,
            // 这里添加你的实际请求...
            {
                val params = hashMapOf<String, Any?>("test" to "data")
                baseService.accountHomeBaseApi(params)
            }
        )
    }
}
