package cn.com.vau.common.mvvm.ext

import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.util.LogUtil
import cn.com.vau.util.UserDataUtil

/**
 * BatchRequestNet 使用指南
 * 
 * 简化后的逻辑特点：
 * 1. 不区分 successResults、failureResults、allResults
 * 2. 统一返回 List<BatchRequestResult<T>>，按请求调用顺序排列
 * 3. 在回调中按索引顺序处理每个接口的返回值
 * 4. 每个接口的返回值中单独处理成功失败逻辑
 */
class BatchRequestNetUsageGuide : BaseViewModel() {

    /**
     * 基础用法：两个请求
     */
    fun basicUsage() {
        batchRequestNet<Any>(
            onComplete = { results ->
                // results[0] 对应第一个请求
                // results[1] 对应第二个请求
                // 按顺序处理每个结果
                
                handleResult(0, results[0]) // 处理用户信息
                handleResult(1, results[1]) // 处理账户数据
            },
            isShowDialog = true,
            // 请求1：用户信息
            {
                val params = hashMapOf<String, Any?>("userId" to UserDataUtil.userId())
                baseService.accountHomeBaseApi(params)
            },
            // 请求2：账户数据
            {
                val params = hashMapOf<String, String>("userId" to UserDataUtil.userId().toString())
                baseService.getAccountSelectApi(params)
            }
        )
    }

    /**
     * 循环处理用法：适合多个相似请求
     */
    fun loopUsage() {
        batchRequestNet<Any>(
            onComplete = { results ->
                // 使用循环统一处理
                results.forEachIndexed { index, result ->
                    handleResult(index, result)
                }
            },
            isShowDialog = true,
            { /* 请求1 */ baseService.accountHomeBaseApi(hashMapOf()) },
            { /* 请求2 */ baseService.getAccountSelectApi(hashMapOf()) },
            { /* 请求3 */ baseService.accountHomeBaseApi(hashMapOf()) }
        )
    }

    /**
     * 实际业务场景：首页数据加载
     */
    fun realWorldUsage() {
        batchRequestNet<Any>(
            onComplete = { results ->
                var allDataLoaded = true
                
                // 按业务逻辑处理每个请求结果
                results.forEachIndexed { index, result ->
                    val success = when (index) {
                        0 -> handleUserData(result)      // 用户基础信息
                        1 -> handleAccountData(result)   // 账户选择数据
                        2 -> handleTradeData(result)     // 交易历史数据
                        else -> false
                    }
                    
                    if (!success) {
                        allDataLoaded = false
                    }
                }
                
                // 根据整体加载结果决定UI显示
                if (allDataLoaded) {
                    showCompleteHomePage()
                } else {
                    showPartialHomePage()
                }
            },
            onError = { throwable ->
                LogUtil.e("Guide", "首页数据加载异常: ${throwable.message}")
                showErrorPage()
            },
            isShowDialog = true,
            // 请求列表
            { loadUserBasicInfo() },
            { loadAccountSelectData() },
            { loadTradeHistoryData() }
        )
    }

    // 统一的结果处理方法
    private fun handleResult(index: Int, result: BatchRequestResult<Any>) {
        if (result.isSuccess) {
            // 网络请求成功
            when (val data = result.data) {
                is ApiResponse<*> -> {
                    if (data.isSuccess()) {
                        // 业务成功
                        LogUtil.d("Guide", "请求$index 成功")
                        processSuccessData(index, data.getResponseData())
                    } else {
                        // 业务失败
                        LogUtil.e("Guide", "请求$index 业务失败: ${data.getResponseMsg()}")
                        processBusinessFailure(index, data.getResponseMsg())
                    }
                }
                else -> {
                    // 其他类型数据
                    LogUtil.d("Guide", "请求$index 返回非ApiResponse数据")
                    processOtherData(index, data)
                }
            }
        } else {
            // 网络请求失败
            LogUtil.e("Guide", "请求$index 网络失败: ${result.error?.message}")
            processNetworkFailure(index, result.error)
        }
    }

    // 业务处理方法
    private fun handleUserData(result: BatchRequestResult<Any>): Boolean {
        return if (result.isSuccess) {
            (result.data as? ApiResponse<*>)?.let { response ->
                if (response.isSuccess()) {
                    LogUtil.d("Guide", "用户数据处理成功")
                    // 保存用户数据、更新UI等
                    true
                } else {
                    LogUtil.e("Guide", "用户数据业务失败")
                    false
                }
            } ?: false
        } else {
            LogUtil.e("Guide", "用户数据网络失败")
            false
        }
    }

    private fun handleAccountData(result: BatchRequestResult<Any>): Boolean {
        return if (result.isSuccess) {
            (result.data as? ApiResponse<*>)?.let { response ->
                if (response.isSuccess()) {
                    LogUtil.d("Guide", "账户数据处理成功")
                    true
                } else {
                    LogUtil.e("Guide", "账户数据业务失败")
                    false
                }
            } ?: false
        } else {
            LogUtil.e("Guide", "账户数据网络失败")
            false
        }
    }

    private fun handleTradeData(result: BatchRequestResult<Any>): Boolean {
        return if (result.isSuccess) {
            (result.data as? ApiResponse<*>)?.let { response ->
                if (response.isSuccess()) {
                    LogUtil.d("Guide", "交易数据处理成功")
                    true
                } else {
                    LogUtil.e("Guide", "交易数据业务失败")
                    false
                }
            } ?: false
        } else {
            LogUtil.e("Guide", "交易数据网络失败")
            false
        }
    }

    // 辅助方法
    private fun processSuccessData(index: Int, data: Any?) {
        LogUtil.d("Guide", "处理请求$index 的成功数据: $data")
    }

    private fun processBusinessFailure(index: Int, message: String) {
        LogUtil.e("Guide", "请求$index 业务失败处理: $message")
    }

    private fun processOtherData(index: Int, data: Any?) {
        LogUtil.d("Guide", "处理请求$index 的其他数据: $data")
    }

    private fun processNetworkFailure(index: Int, error: Throwable?) {
        LogUtil.e("Guide", "请求$index 网络失败处理: ${error?.message}")
    }

    private fun showCompleteHomePage() {
        LogUtil.d("Guide", "显示完整首页")
    }

    private fun showPartialHomePage() {
        LogUtil.d("Guide", "显示部分首页数据")
    }

    private fun showErrorPage() {
        LogUtil.e("Guide", "显示错误页面")
    }

    // 模拟请求方法
    private suspend fun loadUserBasicInfo(): Any {
        val params = hashMapOf<String, Any?>("userId" to UserDataUtil.userId())
        return baseService.accountHomeBaseApi(params)
    }

    private suspend fun loadAccountSelectData(): Any {
        val params = hashMapOf<String, String>("userId" to UserDataUtil.userId().toString())
        return baseService.getAccountSelectApi(params)
    }

    private suspend fun loadTradeHistoryData(): Any {
        val params = hashMapOf<String, String>("userId" to UserDataUtil.userId().toString())
        return baseService.getAccountSelectApi(params) // 示例，实际应该是交易接口
    }
}

/**
 * 使用总结：
 * 
 * 1. 方法签名：
 *    batchRequestNet<T>(
 *        onComplete: (List<BatchRequestResult<T>>) -> Unit,
 *        onError: (Throwable) -> Unit = {},
 *        isShowDialog: Boolean = false,
 *        isAutoDismissDialog: Boolean = true,
 *        vararg requests: suspend () -> T
 *    )
 * 
 * 2. 核心特点：
 *    - 并行执行所有请求
 *    - 等待所有请求完成（包括失败的）
 *    - 按请求顺序返回结果列表
 *    - 每个结果包含 isSuccess、data、error、requestIndex
 * 
 * 3. 处理模式：
 *    - 按索引处理：results[0]、results[1]...
 *    - 循环处理：results.forEachIndexed { index, result -> }
 *    - 每个结果单独判断成功失败
 * 
 * 4. 错误处理：
 *    - 网络错误：result.isSuccess = false, result.error 有值
 *    - 业务错误：result.isSuccess = true, 但 ApiResponse.isSuccess() = false
 */
