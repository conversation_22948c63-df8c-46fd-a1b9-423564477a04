package cn.com.vau.common.mvvm.ext

import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.history.data.RequestParamsWrapper
import cn.com.vau.util.LogUtil
import cn.com.vau.util.UserDataUtil

/**
 * BatchRequestNet 简化使用示例
 * 修复了所有类型不匹配问题的实际可用示例
 */
class BatchRequestNetSimpleExample : BaseViewModel() {

    /**
     * 实际可用的示例：同时获取用户信息和账户选择数据
     */
    fun loadUserDataExample() {
        batchRequestNet<Any>(
            onComplete = { summary ->
                LogUtil.d("BatchExample", "批量请求完成 - 成功:${summary.successCount}, 失败:${summary.failureCount}")
                
                // 处理成功的结果
                summary.successResults.forEach { result ->
                    when (val data = result.data) {
                        is ApiResponse<*> -> {
                            if (data.isSuccess()) {
                                LogUtil.d("BatchExample", "请求${result.requestIndex}成功，数据: ${data.getResponseData()}")
                            } else {
                                LogUtil.e("BatchExample", "请求${result.requestIndex}业务失败: ${data.getResponseMsg()}")
                            }
                        }
                        else -> {
                            LogUtil.d("BatchExample", "请求${result.requestIndex}返回其他类型数据: $data")
                        }
                    }
                }

                // 处理失败的结果
                summary.failureResults.forEach { result ->
                    LogUtil.e("BatchExample", "请求${result.requestIndex}网络失败: ${result.error?.message}")
                }

                // 根据结果决定后续操作
                when {
                    summary.isAllSuccess -> {
                        LogUtil.d("BatchExample", "所有请求成功，可以继续后续操作")
                    }
                    summary.hasAnySuccess -> {
                        LogUtil.d("BatchExample", "部分请求成功，显示可用数据")
                    }
                    else -> {
                        LogUtil.e("BatchExample", "所有请求失败，显示错误页面")
                    }
                }
            },
            onError = { throwable ->
                LogUtil.e("BatchExample", "批量请求执行异常: ${throwable.message}")
            },
            isShowDialog = true,
            isAutoDismissDialog = true,
            // 请求1：获取用户基础信息
            {
                val userParams = hashMapOf<String, Any?>(
                    "userId" to UserDataUtil.userId(),
                    "mt4AccountId" to UserDataUtil.accountCd(),
                    "token" to UserDataUtil.loginToken(),
                    "apkType" to "android"
                )
                baseService.accountHomeBaseApi(userParams)
            },
            // 请求2：获取账户选择数据
            {
                val accountParams = hashMapOf<String, String>(
                    "userId" to UserDataUtil.userId().toString(),
                    "token" to UserDataUtil.loginToken().toString()
                )
                baseService.getAccountSelectApi(accountParams)
            }
        )
    }

    /**
     * 包含交易数据的示例
     */
    fun loadUserAndTradeDataExample() {
        val requests = listOf<suspend () -> Any>(
            // 请求1：用户基础信息
            {
                val userParams = hashMapOf<String, Any?>(
                    "userId" to UserDataUtil.userId(),
                    "token" to UserDataUtil.loginToken()
                )
                baseService.accountHomeBaseApi(userParams)
            },
            // 请求2：交易历史数据
            {
                val tradeParams = RequestParamsWrapper(
                    """{"userId":"${UserDataUtil.userId()}","pageNum":1,"pageSize":10}"""
                )
                tradingService.tradeOrdersListOrderPageApi(tradeParams)
            }
        )

        batchRequestNet(
            requests = requests,
            onComplete = { summary ->
                LogUtil.d("BatchExample", "用户和交易数据加载完成")
                
                // 分别处理不同类型的数据
                summary.successResults.forEach { result ->
                    when (result.requestIndex) {
                        0 -> {
                            // 处理用户数据
                            (result.data as? ApiResponse<*>)?.let { response ->
                                if (response.isSuccess()) {
                                    LogUtil.d("BatchExample", "用户数据加载成功")
                                    // 这里可以更新用户相关的UI
                                }
                            }
                        }
                        1 -> {
                            // 处理交易数据
                            (result.data as? ApiResponse<*>)?.let { response ->
                                if (response.isSuccess()) {
                                    LogUtil.d("BatchExample", "交易数据加载成功")
                                    // 这里可以更新交易相关的UI
                                }
                            }
                        }
                    }
                }
            },
            onError = { throwable ->
                LogUtil.e("BatchExample", "加载失败: ${throwable.message}")
            },
            isShowDialog = true
        )
    }

    /**
     * 错误处理示例
     */
    fun errorHandlingExample() {
        batchRequestNet<Any>(
            onComplete = { summary ->
                // 详细的错误处理逻辑
                if (summary.failureCount > 0) {
                    LogUtil.e("BatchExample", "有${summary.failureCount}个请求失败")
                    
                    // 可以根据失败的请求索引进行特定处理
                    summary.failureResults.forEach { result ->
                        when (result.requestIndex) {
                            0 -> LogUtil.e("BatchExample", "用户信息请求失败")
                            1 -> LogUtil.e("BatchExample", "账户数据请求失败")
                        }
                    }
                }

                // 即使有部分失败，也可以处理成功的数据
                if (summary.hasAnySuccess) {
                    LogUtil.d("BatchExample", "处理${summary.successCount}个成功的请求结果")
                    summary.successResults.forEach { result ->
                        // 处理成功的数据
                        LogUtil.d("BatchExample", "处理请求${result.requestIndex}的成功数据")
                    }
                }
            },
            isShowDialog = false, // 错误处理示例不显示loading
            {
                // 一个可能失败的请求
                val params = hashMapOf<String, Any?>(
                    "userId" to "invalid_user_id", // 故意使用无效参数
                    "token" to "invalid_token"
                )
                baseService.accountHomeBaseApi(params)
            },
            {
                // 另一个可能成功的请求
                val params = hashMapOf<String, String>(
                    "userId" to UserDataUtil.userId().toString(),
                    "token" to UserDataUtil.loginToken().toString()
                )
                baseService.getAccountSelectApi(params)
            }
        )
    }

    /**
     * 简单的两个请求示例
     */
    fun simpleTwoRequestsExample() {
        batchRequestNet<Any>(
            onComplete = { summary ->
                LogUtil.d("BatchExample", "简单示例完成: ${summary.successCount}/${summary.totalCount} 成功")
                
                // 简单处理：只关心是否全部成功
                if (summary.isAllSuccess) {
                    LogUtil.d("BatchExample", "所有数据加载成功，更新UI")
                } else {
                    LogUtil.e("BatchExample", "部分数据加载失败")
                }
            },
            isShowDialog = true,
            // 请求1
            {
                val params1 = hashMapOf<String, Any?>(
                    "userId" to UserDataUtil.userId(),
                    "token" to UserDataUtil.loginToken()
                )
                baseService.accountHomeBaseApi(params1)
            },
            // 请求2
            {
                val params2 = hashMapOf<String, String>(
                    "userId" to UserDataUtil.userId().toString(),
                    "token" to UserDataUtil.loginToken().toString()
                )
                baseService.getAccountSelectApi(params2)
            }
        )
    }
}
