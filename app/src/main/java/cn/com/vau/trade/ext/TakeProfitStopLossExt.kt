package cn.com.vau.trade.ext

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.Spanned
import android.widget.EditText
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.utils.inApp.InAppDataUtil.data
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.trade.ext.TpSlUtil.numFormatDigitsByCurrency
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.mathSub
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.percent
import cn.com.vau.util.widget.dialog.BottomItemBean
import java.util.Locale
import kotlin.math.pow

/**
 * 止盈止损工具类
 */
object TpSlUtil {

    /**
     * 获取汇率
     */
    @JvmStatic
    fun getRate(
        shareProductData: ShareProductData,
        orderType: String
    ): String {

        val symbolList = VAUSdkUtil.symbolList()

        val symbol = shareProductData.symbol.ifNull()

        var currencyType = UserDataUtil.currencyType()

        val isUSC = currencyType == "USC"
        if (isUSC) currencyType = "USD"

        if ("USDT" == currencyType) currencyType = "UST"

        // 订单方向
        val direction = if (OrderUtil.isBuyOfOrder(orderType)) 1.0f else -1.0f

        // 汇率
        var rate = 1.0f
        val length = 8

        if ("forex" == shareProductData.stoplossmodel.lowercase(Locale.getDefault())) {

            if (!symbol.contains(currencyType) || symbol.startsWith(currencyType)) {

                // XXXYYY
                val symbolYYY =
                    if (symbol.length <= 3)
                        ""
                    else if (symbol.length >= 6)
                        symbol.substring(3, 6)
                    else
                        symbol.substring(3, symbol.length)

                val suffixZZ = if (symbol.length > 6) symbol.substring(6) else ""

                var mulPrice: Float = -1f
                var divPrice: Float = -1f

                for (symbolData in symbolList) {
                    if (symbolData.symbol.contains("$symbolYYY$currencyType$suffixZZ")) {
                        mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                        rate = rate.mathMul(mulPrice)
                        break
                    }
                    if (symbolData.symbol.contains("$currencyType$symbolYYY$suffixZZ")) {
                        divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                        rate = rate.mathDiv(divPrice, length)
                        break
                    }
                }

                // !①
                if (mulPrice == -1f && divPrice == -1f) {
                    for (symbolData in symbolList) {
                        if (symbolData.symbol.contains("USD$symbolYYY$suffixZZ")) {
                            divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            rate = rate.mathDiv(divPrice, length)
                            break
                        }
                        if (symbolData.symbol.contains("${symbolYYY}USD$suffixZZ")) {
                            mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            rate = rate.mathMul(mulPrice)
                            break
                        }
                    }

                    // ②
                    if (mulPrice != -1f || divPrice != -1f) {
                        for (symbolData in symbolList) {
                            if (symbolData.symbol.contains("USD$currencyType")) {
                                mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                rate = rate.mathMul(mulPrice)
                                break
                            }
                            if (symbolData.symbol.contains("${currencyType}USD")) {
                                divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                rate = rate.mathDiv(divPrice, length)
                                break
                            }
                        }
                    }

                }

            }

        } else {

            // 预付款货币
            val marginCurrency =
                if (UserDataUtil.isStLogin()) {
                    shareProductData.currency
                } else {
                    if (UserDataUtil.isMT5() || UserDataUtil.isVts())
                        shareProductData.profit_currency
                    else
                        shareProductData.margin_currency
                }

            if (marginCurrency != currencyType) {

                var mulPrice: Float = -1f
                var divPrice: Float = -1f

                for (symbolBean in symbolList) {

                    if (symbolBean.symbol.contains("$marginCurrency$currencyType")) {
                        mulPrice = if (direction == 1f) symbolBean.bid else symbolBean.ask
                        rate = rate.mathMul(mulPrice)
                        break
                    }
                    if (symbolBean.symbol.contains("$currencyType$marginCurrency")) {
                        divPrice = if (direction == 1f) symbolBean.bid else symbolBean.ask
                        rate = rate.mathDiv(divPrice, length)
                        break
                    }

                }

                if (mulPrice == -1f && divPrice == -1f) {

                    for (symbolData in symbolList) {

                        if (symbolData.symbol.contains("USD$marginCurrency")) {
                            divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            rate = rate.mathDiv(divPrice, length)
                            break
                        }
                        if (symbolData.symbol.contains("${marginCurrency}USD")) {
                            mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                            rate = rate.mathMul(mulPrice)
                            break
                        }

                    }

                    if (mulPrice != -1f || divPrice != -1f) {
                        for (symbolData in symbolList) {

                            if (symbolData.symbol.contains("USD$currencyType")) {
                                mulPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                rate = rate.mathMul(mulPrice)
                                break
                            }
                            if (symbolData.symbol.contains("${currencyType}USD")) {
                                divPrice = if (direction == 1f) symbolData.bid else symbolData.ask
                                rate = rate.mathDiv(divPrice, length)
                                break
                            }

                        }
                    }

                }

            }

        }

        if (isUSC) rate = rate.mathMul(100f)

        return rate.toString()
    }

    /**
     * 获取盈亏(根据止盈止损价格)
     * 公式：盈亏=(止盈止损价-开仓价)*手数*合约规模*汇率转换*操作
     */
    fun getPnlByProfitLossPrice(
        shareProductData: ShareProductData,
        openPrice: String,
        volume: String,
        orderType: String,
        closePrice: String,
    ): String {
        val profitLoss = VAUSdkUtil.getProfitLoss(shareProductData, openPrice, volume, orderType, closePrice.ifNull("0")).numCurrencyFormat()
        return profitLoss
    }

    /**
     * 获取止盈止损价格(根据盈亏)
     * 公式：盈亏=(止盈止损价-开仓价)*手数*合约规模*汇率转换*操作
     */
    @JvmStatic
    fun getProfitLossPriceByPnl(
        shareProductData: ShareProductData,
        openPrice: String,
        volume: String,
        orderType: String,
        profitLoss: String,
        digits: Int = 0
    ): String {
        if (profitLoss.isEmpty()) {
            return openPrice
        }

        // 订单方向
        val direction = if (OrderUtil.isBuyOfOrder(orderType)) 1.0f else -1.0f

        /**
         * Buy单
         * A=现价*手数*合约规模*汇率    四舍五入
         * B=开仓价*手数*合约规模*汇率   四舍五入
         * 利润=A-B
         *
         * Sell单
         * A=现价*手数*合约规模*汇率     四舍五入
         * B=开仓价*手数*合约规模*汇率   四舍五入
         * 利润=B-A
         */
        // 汇率
        val rate = getRate(shareProductData, orderType)
        // 手数 * 合约规模 * 汇率
        val plParam =
            volume.mathMul(shareProductData.contractsize ?: "1", direction.toString(), rate)
        // 盈亏
//        var profitLoss = plParam.mathMul(closePrice).mathSub(plParam.mathMul(openPrice)).toFloatCatching()
        val closePrice = profitLoss.mathDiv(plParam, 8).mathAdd(openPrice)
        if (closePrice.mathCompTo("0") != 1) {
            return "0".numFormat(digits)
        }
        return closePrice.numFormat(digits)
    }

    /**
     * 获取涨跌幅（%前面的数值）
     * 公式：止盈止损价格=开仓价格*(1+涨跌幅)
     */
    @JvmStatic
    fun getOffset(
        openPrice: String,
        closePrice: String
    ): String {
        if (closePrice.isEmpty()) {
            return "-100.00"
        }
        val offset = closePrice.mathDiv(openPrice, 8).mathSub("1").percent().numFormat(2)
        return offset
    }

    /**
     * 获取止盈止损价格(涨跌幅)
     * 公式：止盈止损价格=开仓价格*(1+涨跌幅)
     */
    @JvmStatic
    fun getProfitLossPriceByOffset(
        openPrice: String,
        offset: String,
        digits: Int = 0
    ): String {
        if (offset.isEmpty()) {
            return openPrice
        }
        val closePrice = openPrice.mathMul("1".mathAdd(offset.mathDiv("100", 8))).numFormat(digits)
        if (closePrice.mathCompTo("0") != 1) {
            return "0"
        }
        return closePrice
    }

    /**
     * 检查止盈价格是否合法
     */
    fun checkTakeProfit(
        context: Context,
        takeProfit: String,
        takeProfitRange: String,
        tpCompareResults: Int,
        emptyBlock: (() -> Unit)? = null,
        validBlock: (() -> Unit)? = null,
        invalidBlock: ((tipStr: String) -> Unit)? = null
    ): Boolean {
        if (takeProfit.isEmpty()) {
            emptyBlock?.invoke()
            return false
        } else if (takeProfit.mathCompTo(takeProfitRange) == tpCompareResults) {
            val tipStr = if (tpCompareResults == 1) {
                "${context.getString(R.string.max_value)}:${takeProfitRange}"
            } else {
                "${context.getString(R.string.min_value)}:${takeProfitRange}"
            }
            invalidBlock?.invoke(tipStr)
            return false
        } else {
            validBlock?.invoke()
            return true
        }
    }

    /**
     * 检查止损价格是否合法
     */
    fun checkStopLoss(
        context: Context,
        stopLoss: String,
        stopLossRange: String,
        slCompareResults: Int,
        emptyBlock: (() -> Unit)? = null,
        validBlock: (() -> Unit)? = null,
        invalidBlock: ((tipStr: String) -> Unit)? = null
    ): Boolean {
        if (stopLoss.isEmpty()) {
            emptyBlock?.invoke()
            return false
        } else if (stopLoss.mathCompTo(stopLossRange) == slCompareResults) {
            val tipStr = if (slCompareResults == 1) {
//                context.getString(R.string.price_should_be_lower_than_equal_to_x, stopLossRange)
                "${context.getString(R.string.max_value)}:${stopLossRange}"
            } else {
//                context.getString(R.string.price_should_be_higher_than_equal_to_x, stopLossRange)
                "${context.getString(R.string.min_value)}:${stopLossRange}"
            }
            invalidBlock?.invoke(tipStr)
            return false
        } else {
            validBlock?.invoke()
            return true
        }
    }

    /**
     * 检查停损价格范围
     */
    fun checkStopPriceRange(
        context: Context,
        stopPrice: String,
        stopPriceRange: String,
        cmd: String,
        emptyBlock: ((tipStr: String) -> Unit)? = null,
        validBlock: (() -> Unit)? = null,
        invalidBlock: ((tipStr: String) -> Unit)? = null
    ): Boolean {
        val atCompareResults = when (cmd) {
            "2" -> 1
            "3", "4", "6" -> -1
            else -> 1
        }
        if (stopPrice.isEmpty()) {
            val tipStr = if (atCompareResults == 1) {
                "${context.getString(R.string.max_value)}:${stopPriceRange}"
            } else {
                "${context.getString(R.string.min_value)}:${stopPriceRange}"
            }
            emptyBlock?.invoke(tipStr)
            return false
        } else if (stopPrice.mathCompTo(stopPriceRange) == atCompareResults) {
            val tipStr = if (atCompareResults == 1) {
                "${context.getString(R.string.max_value)}:${stopPriceRange}"
            } else {
                "${context.getString(R.string.min_value)}:${stopPriceRange}"
            }
            invalidBlock?.invoke(tipStr)
            return false
        } else {
            validBlock?.invoke()
            return true
        }

    }

    /**
     * 检查限价价格范围
     */
    fun checkLimitPriceRange(
        context: Context,
        limitPrice: String,
        limitPriceRange: String,
        cmd: String,
        emptyBlock: ((tipStr: String) -> Unit)? = null,
        validBlock: (() -> Unit)? = null,
        invalidBlock: ((tipStr: String) -> Unit)? = null
    ): Boolean {
        val compareResults = if (OrderUtil.isBuyOfOrder(cmd)) 1 else -1
        if (limitPrice.isEmpty()) {
            val tipStr = if (compareResults == 1) {
                "${context.getString(R.string.max_value)}:${limitPriceRange}"
            } else {
                "${context.getString(R.string.min_value)}:${limitPriceRange}"
            }
            emptyBlock?.invoke(tipStr)
            return false
        } else if (limitPrice.mathCompTo(limitPriceRange) == compareResults) {
            val tipStr = if (compareResults == 1) {
                "${context.getString(R.string.max_value)}:${limitPriceRange}"
            } else {
                "${context.getString(R.string.min_value)}:${limitPriceRange}"
            }
            invalidBlock?.invoke(tipStr)
            return false
        } else {
            validBlock?.invoke()
            return true
        }
    }

    fun isStopLimit(orderBean: ShareOrderData): Boolean {
        return (orderBean.cmd == "6" || orderBean.cmd == "7")
    }

    /**
     * 挂单Stop Price范围
     *
     * cmd
     * 0-buy
     * 1-sell
     * 2-buy limit  买入限价
     * 3-sell limit 卖出限价
     * 4-buy stop   买入提损
     * 5-sell stop  卖出停损
     * 6-buy stop limit   买入停损限价
     * 7-sell stop limit  卖出停损限价
     */
//    fun getStopPriceRange(orderBean: ShareOrderData, productBean: ShareProductData): String {
//        val stopLossLevel = productBean.stopLossLevel()
//        val digits = productBean.digits
//        if (orderBean.cmd == "7" || orderBean.cmd == "5" || orderBean.cmd == "3") {
//            // Sell
//            val atPrice = if (orderBean.cmd == "5" || orderBean.cmd == "7") {
//                "${productBean.bid}".mathSub(stopLossLevel).numFormat(digits, false)
//            } else {
//                "${productBean.bid}".mathAdd(stopLossLevel).numFormat(digits, false)
//            }
//            return atPrice
//        } else {
//            //Buy
//            val atPrice = if (orderBean.cmd == "4" || orderBean.cmd == "6") {
//                "${productBean.ask}".mathAdd(stopLossLevel).numFormat(digits, false)
//            } else {
//                "${productBean.ask}".mathSub(stopLossLevel).numFormat(digits, false)
//            }
//            return atPrice
//        }
//    }

    /**
     * 挂单Limit Price范围
     */
//    fun getLimitPriceRange(orderBean: ShareOrderData, productBean: ShareProductData, stopPrice: String): String {
//        val stopLossLevel = productBean.stopLossLevel()
//        val digits = productBean.digits
//        val range = if (orderBean.cmd == "7" || orderBean.cmd == "5" || orderBean.cmd == "3") {
//            //Sell
//            (stopPrice.mathAdd(stopLossLevel)).numFormat(digits, false).ifNull()
//        } else {
//            //Buy
//            (stopPrice.mathSub(stopLossLevel)).numFormat(digits, false).ifNull()
//        }
//        return range
//    }

    /**
     * 止盈止损计算方式弹窗数据
     */
    fun createComputeModeMap(context: Context): LinkedHashMap<BottomItemBean, ComputeMode> {
        return linkedMapOf(
            BottomItemBean(
                context.getString(R.string.pnl),
                context.getString(R.string.set_tp_sl_prices_based_estimated_pnl)
            ) to ComputeMode.PNL,
            BottomItemBean(
                context.getString(R.string.offset_percent),
                context.getString(R.string.set_tp_sl_prices_based_entry_price)
            ) to ComputeMode.OFFSET
        )
    }

    /**
     * 根据货币的类型进行格式化时的小数位
     */
    fun numFormatDigitsByCurrency(currencyType: String): Int =
        when (currencyType) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        }

    /**
     * 字符串中有n个相同子串，获取第n个相同子串的起始位置
     * 比如：
     * 字符串："When the Last Price reaches --, it will trigger a Market Order, and the estimated PnL will be --"
     * 查找第2个--位置
     */

    fun findNthSubstringIndexOrLast(str: String, substring: String, n: Int): Int {
        if (n <= 0 || substring.isEmpty() || str.isEmpty()) {
            return -1
        }

        var currentIndex = -substring.length
        var lastFoundIndex = -1
        var count = 0

        while (true) {
            currentIndex = str.indexOf(substring, startIndex = currentIndex + substring.length)
            if (currentIndex == -1) break // 找不到更多子串

            count++
            lastFoundIndex = currentIndex

            if (count == n) return currentIndex // 找到第 N 个，直接返回
        }

        return if (count > 0) lastFoundIndex else -1 // 找不到第 N 个，返回最后一个（或 -1）
    }

}

/**
 * 交易类型
 */
enum class TpSlTradeType {
    TRADE_SELL, TRADE_BUY
}

/**
 * true: TradeType.TRADE_BUY
 * false: TradeType.TRADE_SELL
 */
fun Boolean.toTradeType(): TpSlTradeType {
    val isBuy = this
    return if (isBuy) TpSlTradeType.TRADE_BUY else TpSlTradeType.TRADE_SELL
}

/**
 * 停损or限价标签
 */
fun ShareOrderData.toStopOrLimitTag(): String {
    return when (cmd) {
        "2", "3" -> "Limit"
        "4", "5" -> "Stop"
        "6", "7" -> "Stop Limit"
        else -> ""
    }
}

/**
 * viewAtPrice 输入框标题
 */
fun ShareOrderData.toAtPriceTitle(context: Context): String {
    return when (cmd) {
        // TODO: 2025/5/29 11:20 @array.zhou 是否需要多语言翻译
        "2", "3" -> context.getString(R.string.limit_price)
        "4", "5" -> context.getString(R.string.stop_price)
        "6", "7" -> context.getString(R.string.stop_price)
        else -> ""
    }
}

/**
 * true：停损单
 */
fun ShareOrderData.isOnlyStopOrder(): Boolean {
    return when (cmd) {
        "2", "3" -> false
        "4", "5" -> true
        "6", "7" -> false
        else -> false
    }
}

/**
 * true：限价单
 */
fun ShareOrderData.isOnlyLimitOrder(): Boolean {
    return when (cmd) {
        "2", "3" -> true
        "4", "5" -> false
        "6", "7" -> false
        else -> false
    }
}

/**
 * true：停损限价单
 */
fun ShareOrderData.isStopLimitOrder(): Boolean {
    return when (cmd) {
        "2", "3" -> false
        "4", "5" -> false
        "6", "7" -> true
        else -> false
    }
}

/**
 * 计算市价的止盈
 * 买入：止损 低于等于（卖出价-止损水平）；止盈：大于等于（卖出价+止损水平）
 * 卖出：止损 大于等于（买入价+止损水平）；止盈：低于等于（买入价-止损水平）
 */
fun ShareProductData.computeMarketTakeProfitRange(
    tradeType: TpSlTradeType,
    stopLossLevel: String
): String {
    var takeProfitRange = ""
    // 止盈止损范围
    if (tradeType == TpSlTradeType.TRADE_SELL) {
        // sell
        // 止盈范围（低于等于（买入价-止损水平））
        val stopLossLevelYES = "${this.ask}".mathSub(stopLossLevel)
        takeProfitRange = stopLossLevelYES.numFormat(digits)
        return takeProfitRange
    } else {
        // buy
        // 止盈范围（大于等于（卖出价+止损水平））
        val stopLossLevelYES = "${this.bid}".mathAdd(stopLossLevel)
        takeProfitRange = stopLossLevelYES.numFormat(digits)
    }
    return takeProfitRange
}

/**
 * 计算市价的止损
 * 买入：止损 低于等于（卖出价-止损水平）；止盈：大于等于（卖出价+止损水平）
 * 卖出：止损 大于等于（买入价+止损水平）；止盈：低于等于（买入价-止损水平）
 */
fun ShareProductData.computeMarketStopLoss(
    tradeType: TpSlTradeType,
    stopLossLevel: String,
): String {
    var stopLossRange = ""
    // 止盈止损范围
    if (tradeType == TpSlTradeType.TRADE_SELL) {
        // sell
        // 止损范围（大于等于（买入价+止损水平））
        val stopLossLevelNO = "${this.ask}".mathAdd(stopLossLevel)
        stopLossRange = stopLossLevelNO.numFormat(digits)
    } else {
        // buy
        // 止损范围（低于等于（卖出价-止损水平））
        val stopLossLevelNO = "${this.bid}".mathSub(stopLossLevel)
        stopLossRange = stopLossLevelNO.numFormat(digits)
    }
    return stopLossRange
}

/**
 * 挂单刷新挂单价格范围
 * Buy Limit ：挂单价 低于等于（买入价-止损水平）
 * Buy Stop: 挂单价 大于等于（买入价+止损水平）
 * Buy Stop Limit:  挂单价 大于等于（买入价+止损水平）
 *
 * Sell Limit: 挂单价 大于等于（卖出价+止损水平）
 * Sell Stop:    挂单价   低于等于（卖出价-止损水平）
 * Sell Stop Limit:   挂单价  低于等于（卖出价-止损水平）
 *
 */
fun ShareProductData.computeAtPriceRange(
    cmd: String,
    stopLossLevel: String,
): String {
    if (OrderUtil.isBuyOfOrder(cmd).not()) {
        // Sell
        val atPriceRange = if (cmd == "5" || cmd == "7")
            "$bid".mathSub(stopLossLevel).numFormat(digits, false)
        else
            "$bid".mathAdd(stopLossLevel).numFormat(digits, false)
        return atPriceRange
    } else {
        //Buy
        val atPriceRange = if (cmd == "4" || cmd == "6")
            "$ask".mathAdd(stopLossLevel).numFormat(digits, false)
        else
            "$ask".mathSub(stopLossLevel).numFormat(digits, false)
        return atPriceRange
    }
}

/**
 * 挂单刷新挂单价格范围
 * Buy Limit ：挂单价 低于等于（买入价-止损水平）
 * Buy Stop: 挂单价 大于等于（买入价+止损水平）
 * Buy Stop Limit:  挂单价 大于等于（买入价+止损水平）
 *
 * Sell Limit: 挂单价 大于等于（卖出价+止损水平）
 * Sell Stop:    挂单价   低于等于（卖出价-止损水平）
 * Sell Stop Limit:   挂单价  低于等于（卖出价-止损水平）
 */
fun ShareProductData.computeLimitPriceRange(
    atPrice: String,
    cmd: String,
    stopLossLevel: String,
): String {
    if (OrderUtil.isBuyOfOrder(cmd).not()) {
        // Sell
        val limitPriceRange = (atPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        return limitPriceRange
    } else {
        //Buy
        val limitPriceRange = (atPrice.mathSub(stopLossLevel)).numFormat(digits, false)
        return limitPriceRange
    }
}

/**
 * 计算挂单的止盈范围
 * Buy Limit：止盈 大于等于（挂单价+止损水平）
 * Buy Stop：  止盈    大于等于（挂单价+止损水平）
 * Buy Stop Limit 止盈  大于等于（Stop Limit 价+止损水平
 *
 * Sell Limit：   止盈 低于等于（挂单价-止损水平）
 * Sell Stop：     止盈 低于等于（挂单价-止损水平）
 * Sell Stop Limit 止盈  低于等于（Stop Limit 价-止损水平）
 *
 *
 */
fun ShareProductData.computePendingTakeProfitRange(
    atPrice: String,
    stopLimitPrice: String,
    cmd: String,
    stopLossLevel: String,
): String {
    if (OrderUtil.isBuyOfOrder(cmd).not()) {
        // Sell
        val takeProfitRange = if (cmd == "7") {
            (stopLimitPrice.mathSub(stopLossLevel)).numFormat(digits, false)
        } else {
            (atPrice.mathSub(stopLossLevel)).numFormat(digits, false)
        }
        return takeProfitRange
    } else {
        //Buy
        val takeProfitRange = if (cmd == "6") {
            (stopLimitPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        } else {
            (atPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        }
        return takeProfitRange
    }
}

/**
 * 计算挂单的止损范围
 * Buy Limit：止损 低于等于（挂单价-止损水平）
 * Buy Stop：  止损 低于等于（挂单价-止损水平）
 * Buy Stop Limit 止损 低于等于（Stop Limit 价-止损水平）
 *
 * Sell Limit：   止损 大于等于（挂单价+止损水平）
 * Sell Stop：     止损 大于等于（挂单价+止损水平）
 * Sell Stop Limit 止损 大于等于（Stop Limit 价+止损水平）
 *
 */
fun ShareProductData.computePendingStopLossRange(
    atPrice: String,
    stopLimitPrice: String,
    cmd: String,
    stopLossLevel: String,
): String {
    if (OrderUtil.isBuyOfOrder(cmd).not()) {
        // Sell
        val stopLossRange = if (cmd == "7") {
            (stopLimitPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        } else {
            (atPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        }
        return stopLossRange
    } else {
        //Buy
        val takeProfitRange = if (cmd == "6") {
            (stopLimitPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        } else {
            (atPrice.mathAdd(stopLossLevel)).numFormat(digits, false)
        }
        return takeProfitRange
    }
}

/**
 * 计算挂单预估盈利和预估亏损
 */
fun computeEstimatedProfitLoss(
    orderBean: ShareOrderData,
    productBean: ShareProductData,
    atPriceOrLimitPrice: String,
    closePrice: String,
): String {
    val profitLoss = VAUSdkUtil.getProfitLoss(
        productBean,
        atPriceOrLimitPrice,
        orderBean.volume ?: "0",
        orderBean.cmd.ifNull(),
        closePrice
    ).toString().numCurrencyFormat()
    return profitLoss
}

/**
 * 止损水平= 止损位 / 10的小数位次方
 */
fun ShareProductData.stopLossLevel(): String {
    return this.stopslevel.mathDiv("${10.0.pow(this.digits)}", this.digits + 1)
}

/**
 * Stop Price范围提示前缀
 */
fun ShareOrderData.toStopPriceRangePrefix(context: Context): String {
    return when (cmd) {
        "2" -> "${context.getString(R.string.max_value)}:"
        "3", "4", "6" -> "${context.getString(R.string.min_value)}:"
        else -> "${context.getString(R.string.max_value)}:"
    }
}

/**
 * Limit Price范围提示前缀
 */
fun ShareOrderData.toLimitPriceRangePrefix(context: Context): String {
    return when (cmd) {
        // Sell
        "7", "5", "3" -> "${context.getString(R.string.min_value)}:"
        //Buy
        else -> "${context.getString(R.string.max_value)}:"
    }
}

/**
 * 止盈、止损价格小数位检查
 * 1、检查输入的价格字符串中是否包含小数点，如果包含则进一步检查小数点后的位数是否超过指定的小数位数，如果超过则删除多余的部分。
 * 2、如果小数点前的数字长度超过9位，则删除小数点前多余的数字。
 * 3、如果输入的价格字符串中不包含小数点，且字符串长度超过9位，则删除最后一位数字。
 *
 * @param digits 指定的小数位数
 */
fun Editable.checkTakeProfitDigits(digits: Int) {
    runCatching {
        val temp = this.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (temp.length - posDot - 1 > digits) {
                this.delete(posDot + digits + 1, posDot + 2 + digits)
            }
            if (posDot > 9 && posDot - 1 in 0..this.length && posDot in 0..this.length)
                this.delete(posDot - 1, posDot)
        } else {
            if (temp.length > 9)
                this.delete(temp.length - 1, temp.length)
        }
    }.onFailure {
        it.printStackTrace()
    }
}

/**
 * 计算方式
 */
enum class ComputeMode {
    PNL,
    OFFSET
}

fun ComputeMode.toModeActionText(context: Context): String {
    return when (this) {
        ComputeMode.PNL -> context.getString(R.string.pnl)
        ComputeMode.OFFSET -> context.getString(R.string.offset_percent)
    }
}

fun ComputeMode.toModeEditTitle(context: Context): String {
    return when (this) {
        ComputeMode.PNL -> "${context.getString(R.string.pnl).arabicReverseTextByFlag(" ")} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ").ifNull()
        ComputeMode.OFFSET -> context.getString(R.string.offset_percent)
    }
}

fun ComputeMode.toTraceModeType(): String {
    return when (this) {
        ComputeMode.PNL -> "PNL"
        ComputeMode.OFFSET -> "Offset%"
    }
}

/**
 * 计算方式输入框小数位
 * 1、PNL：根据产品配置
 * 2、Offset：2位
 */
fun ComputeMode.toDigits(): Int {
    return when (this) {
        ComputeMode.PNL -> numFormatDigitsByCurrency(UserDataUtil.currencyType())
        ComputeMode.OFFSET -> 2
    }
}

/**
 * 编辑框状态
 */
enum class EditState {
    NORMAL, FOCUS, ERROR
}

fun EditState.toEditStateDrawable(): Int {
    return when (this) {
        EditState.FOCUS -> R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10
        EditState.ERROR -> R.drawable.draw_shape_stroke_cf44040_solid_c0a1e1e1e_c262930_r10
        else -> R.drawable.draw_shape_c0a1e1e1e_c262930_r10
    }
}

/**
 * 输入框只能输入数字
 */
fun EditText.setDecimalFilter() {
    val filter = object : InputFilter {

        override fun filter(
            source: CharSequence?,
            start: Int,
            end: Int,
            dest: Spanned?,
            dstart: Int,
            dend: Int
        ): CharSequence? {
            return try {
                val input = dest?.subSequence(0, dstart).toString() +
                        source?.subSequence(start, end) +
                        dest?.subSequence(dend, dest.length).toString()
                if (input.startsWith("+") || input.startsWith("-.")) {
                    ""
                } else {
                    null// 返回 null 表示接受输入
                }
            } catch (e: NumberFormatException) {
                "" // 返回空字符串表示拒绝输入
            }
        }

    }

    filters = arrayOf(filter)
}