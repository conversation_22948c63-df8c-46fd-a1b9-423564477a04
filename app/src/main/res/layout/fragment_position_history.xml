<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSymbols"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:drawableEnd="@drawable/icon2_expand_down"
        android:drawablePadding="4dp"
        android:text="@string/symbols"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFilter"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:padding="6dp"
        android:text="@string/symbols"
        app:layout_constraintBottom_toBottomOf="@id/tvSymbols"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvSymbols"
        app:srcCompat="@drawable/draw_bitmap2_icon_sourse2_community_filters_ca61e1e1e_c99ffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimePeriod"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSymbols"
        tools:text="12/11/2024 - 20/12/2024" />

    <ImageView
        android:id="@+id/ivTriangleDown"
        android:layout_width="20dp"
        android:layout_height="12dp"
        android:contentDescription="@string/app_name"
        android:paddingHorizontal="4dp"
        android:src="@drawable/icon2_expand_down"
        app:layout_constraintBottom_toBottomOf="@+id/tvTimePeriod"
        app:layout_constraintStart_toEndOf="@+id/tvTimePeriod"
        app:layout_constraintTop_toTopOf="@+id/tvTimePeriod" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPnl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r6"
        android:padding="12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTimePeriod"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvPnlTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="@string/pnl" />

        <TextView
            android:id="@+id/tvPnl"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/ce35728"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvPnlTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvPnlTitle"
            tools:ignore="SpUsage"
            tools:text="-0.24" />

        <TextView
            android:id="@+id/tvNetPnlTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPnlTitle"
            tools:ignore="SpUsage"
            tools:text="@string/net_pnl" />

        <TextView
            android:id="@+id/tvNetPnl"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/ce35728"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvNetPnlTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvNetPnlTitle"
            tools:ignore="SpUsage"
            tools:text="-0.30" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clPnl">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                tools:itemCount="2"
                tools:listitem="@layout/item_position_history" />

            <ViewStub
                android:id="@+id/mEmptyView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:layout="@layout/vs_layout_no_data" />

            <ViewStub
                android:id="@+id/mErrorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:layout="@layout/vs_layout_no_data" />

        </FrameLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
